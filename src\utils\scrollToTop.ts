
export const scrollToTop = () => {
  // Enhanced scroll to top function for better cross-browser compatibility
  // and immediate scroll behavior for page navigation
  try {
    // First attempt: Modern browsers with smooth scrolling
    window.scrollTo({
      top: 0,
      left: 0,
      behavior: 'smooth'
    });
    
    // Fallback for older browsers or if smooth scrolling fails
    if (window.pageYOffset > 0) {
      setTimeout(() => {
        window.scrollTo(0, 0);
      }, 10);
    }
    
    // Additional fallback for document element
    if (document.documentElement.scrollTop > 0) {
      document.documentElement.scrollTop = 0;
    }
    
    // Additional fallback for body element
    if (document.body.scrollTop > 0) {
      document.body.scrollTop = 0;
    }
  } catch (error) {
    // Final fallback - immediate scroll without smooth behavior
    console.warn('Smooth scroll failed, using immediate scroll:', error);
    window.scrollTo(0, 0);
    document.documentElement.scrollTop = 0;
    document.body.scrollTop = 0;
  }
};
