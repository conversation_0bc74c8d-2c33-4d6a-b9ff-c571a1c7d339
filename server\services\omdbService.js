/**
 * Server-side OMDb API Service
 * 
 * Comprehensive service for interacting with the Open Movie Database API from the backend
 * Handles movies and TV series search functionality
 */

const axios = require('axios');
require('dotenv').config();

// OMDb API Configuration
const OMDB_API_KEY = process.env.VITE_OMDB_API_KEY || process.env.OMDB_API_KEY || '';
const OMDB_BASE_URL = process.env.VITE_OMDB_BASE_URL || process.env.OMDB_BASE_URL || 'https://www.omdbapi.com';

// TMDB API Configuration for enhanced poster and trailer fetching
const TMDB_API_KEY = process.env.VITE_TMDB_API_KEY || process.env.TMDB_API_KEY || '';
const TMDB_BASE_URL = process.env.VITE_TMDB_BASE_URL || process.env.TMDB_BASE_URL || 'https://api.themoviedb.org/3';
const TMDB_IMAGE_BASE_URL = process.env.VITE_TMDB_IMAGE_BASE_URL || 'https://image.tmdb.org/t/p';

// Rate limiting configuration
const RATE_LIMIT_DELAY = 200; // 5 requests per second with safety margin
let lastRequestTime = 0;
let lastTMDBRequestTime = 0;

// Custom error class for OMDb API errors
class OMDbError extends Error {
  constructor(message, statusCode = null) {
    super(message);
    this.name = 'OMDbError';
    this.statusCode = statusCode;
  }
}

// Rate limiting function
async function rateLimitedRequest() {
  const now = Date.now();
  const timeSinceLastRequest = now - lastRequestTime;
  
  if (timeSinceLastRequest < RATE_LIMIT_DELAY) {
    const delay = RATE_LIMIT_DELAY - timeSinceLastRequest;
    await new Promise(resolve => setTimeout(resolve, delay));
  }
  
  lastRequestTime = Date.now();
}

// Rate limiting function for TMDB requests
async function rateLimitedTMDBRequest() {
  const now = Date.now();
  const timeSinceLastRequest = now - lastTMDBRequestTime;

  if (timeSinceLastRequest < 250) { // TMDB rate limit
    const delay = 250 - timeSinceLastRequest;
    await new Promise(resolve => setTimeout(resolve, delay));
  }

  lastTMDBRequestTime = Date.now();
}

// Enhanced poster and trailer fetching using TMDB
async function getEnhancedMediaData(imdbId) {
  if (!TMDB_API_KEY) {
    return { posterUrl: '', trailer: '' };
  }

  try {
    await rateLimitedTMDBRequest();

    // Search TMDB using IMDb ID
    const findUrl = `${TMDB_BASE_URL}/find/${imdbId}?api_key=${TMDB_API_KEY}&external_source=imdb_id`;
    const findResponse = await axios.get(findUrl);
    const findData = findResponse.data;

    let tmdbId = null;
    let contentType = 'movie';

    // Check if we found the content
    if (findData.movie_results && findData.movie_results.length > 0) {
      tmdbId = findData.movie_results[0].id;
      contentType = 'movie';
    } else if (findData.tv_results && findData.tv_results.length > 0) {
      tmdbId = findData.tv_results[0].id;
      contentType = 'tv';
    }

    if (!tmdbId) {
      return { posterUrl: '', trailer: '' };
    }

    await rateLimitedTMDBRequest();

    // Get detailed information including videos
    const detailsUrl = `${TMDB_BASE_URL}/${contentType}/${tmdbId}?api_key=${TMDB_API_KEY}&append_to_response=videos,images`;
    const detailsResponse = await axios.get(detailsUrl);
    const detailsData = detailsResponse.data;

    // Get HD poster URL
    let posterUrl = '';
    if (detailsData.poster_path) {
      posterUrl = `${TMDB_IMAGE_BASE_URL}/w780${detailsData.poster_path}`; // HD poster
    }

    // Get YouTube trailer URL
    let trailer = '';
    if (detailsData.videos && detailsData.videos.results) {
      const trailers = detailsData.videos.results.filter(video =>
        video.site === 'YouTube' &&
        (video.type === 'Trailer' || video.type === 'Teaser')
      );

      if (trailers.length > 0) {
        // Prefer official trailers, then any trailer
        const officialTrailer = trailers.find(t => t.official === true) || trailers[0];
        trailer = `https://www.youtube.com/embed/${officialTrailer.key}`;
      }
    }

    return { posterUrl, trailer };
  } catch (error) {
    console.error('Error fetching enhanced media data from TMDB:', error);
    return { posterUrl: '', trailer: '' };
  }
}

// Generic API request function with error handling
async function omdbRequest(params = {}) {
  if (!OMDB_API_KEY) {
    throw new OMDbError('OMDb API key is not configured');
  }

  await rateLimitedRequest();

  const requestParams = {
    apikey: OMDB_API_KEY,
    ...params
  };

  try {
    const response = await axios.get(OMDB_BASE_URL, { params: requestParams });
    const data = response.data;
    
    if (data.Response === 'False') {
      throw new OMDbError(data.Error || 'OMDb API request failed');
    }
    
    return data;
  } catch (error) {
    if (error instanceof OMDbError) {
      throw error;
    }
    if (error.response) {
      const statusCode = error.response.status;
      const message = error.response.data?.Error || `HTTP ${statusCode} error`;
      throw new OMDbError(message, statusCode);
    } else if (error.request) {
      throw new OMDbError('Network error: Unable to reach OMDb API');
    } else {
      throw new OMDbError(`Request error: ${error.message}`);
    }
  }
}

// Helper function to determine if poster is available
function hasValidPoster(poster) {
  return poster && poster !== 'N/A' && poster.startsWith('http');
}

// Helper function to format OMDb data for display with comprehensive field mapping
function formatOMDbData(movie, enhancedData = {}) {
  // Extract and clean runtime
  const cleanRuntime = movie.Runtime && movie.Runtime !== 'N/A' ? 
    movie.Runtime.replace(/[^\d]/g, '') : '';
  
  // Extract and process genres
  const genreList = movie.Genre && movie.Genre !== 'N/A' ? 
    movie.Genre.split(', ').filter(g => g.trim()) : [];
  
  // Extract and process languages
  const languageList = movie.Language && movie.Language !== 'N/A' ? 
    movie.Language.split(', ').filter(l => l.trim()) : [];
  
  // Extract and process cast
  const castList = movie.Actors && movie.Actors !== 'N/A' ? 
    movie.Actors.split(', ').filter(a => a.trim()) : [];
  
  // Extract and process writers
  const writerList = movie.Writer && movie.Writer !== 'N/A' ? 
    movie.Writer.split(', ').filter(w => w.trim()) : [];
  
  // Extract director(s)
  const directorList = movie.Director && movie.Director !== 'N/A' ? 
    movie.Director.split(', ').filter(d => d.trim()) : [];
  
  // Extract country information
  const countryList = movie.Country && movie.Country !== 'N/A' ? 
    movie.Country.split(', ').filter(c => c.trim()) : [];
  
  // Determine studio from available data
  let studio = '';
  if (movie.Production && movie.Production !== 'N/A') {
    studio = movie.Production;
  } else if (directorList.length > 0) {
    studio = directorList[0];
  }
  
  return {
    id: movie.imdbID,
    title: movie.Title || '',
    year: movie.Year && movie.Year !== 'N/A' ? movie.Year : '',
    type: movie.Type,
    plot: movie.Plot && movie.Plot !== 'N/A' ? movie.Plot : '',
    description: movie.Plot && movie.Plot !== 'N/A' ? movie.Plot : '',
    poster: enhancedData.posterUrl || (hasValidPoster(movie.Poster) ? movie.Poster : ''),
    posterUrl: enhancedData.posterUrl || (hasValidPoster(movie.Poster) ? movie.Poster : ''),
    thumbnailUrl: enhancedData.posterUrl || (hasValidPoster(movie.Poster) ? movie.Poster : ''),
    coverImage: enhancedData.posterUrl || (hasValidPoster(movie.Poster) ? movie.Poster : ''),
    rating: movie.imdbRating && movie.imdbRating !== 'N/A' ? movie.imdbRating : '',
    imdbRating: movie.imdbRating && movie.imdbRating !== 'N/A' ? movie.imdbRating : '',
    runtime: cleanRuntime,
    genres: genreList,
    director: directorList.join(', '),
    studio: studio,
    languages: languageList,
    country: countryList.join(', '),
    actors: movie.Actors && movie.Actors !== 'N/A' ? movie.Actors : '',
    cast: castList,
    awards: movie.Awards && movie.Awards !== 'N/A' ? movie.Awards : '',
    released: movie.Released && movie.Released !== 'N/A' ? movie.Released : '',
    totalSeasons: movie.Type === 'series' && movie.totalSeasons && movie.totalSeasons !== 'N/A' ?
      movie.totalSeasons : null,
    trailer: enhancedData.trailer || '',
    tags: genreList,
    crew: directorList.map(d => `${d} (Director)`).concat(writerList.map(w => `${w} (Writer)`)),
    writers: writerList,
    producers: [],
    audioTracks: languageList,
    quality: [],
    subtitleUrl: '',
    // Additional metadata
    boxOffice: movie.BoxOffice || '',
    metascore: movie.Metascore || '',
    rottenTomatoes: movie.Ratings?.find(r => r.Source === 'Rotten Tomatoes')?.Value || '',
    rated: movie.Rated || '',
    dvd: movie.DVD || '',
    website: movie.Website || '',
  };
}

// Convert OMDb type to content type
function convertOMDbType(omdbType) {
  return omdbType === 'series' ? 'tv' : 'movie';
}

// Get detailed information by IMDb ID
async function getContentByImdbId(imdbId) {
  try {
    // Clean the IMDb ID (remove 'tt' prefix if present, then add it back)
    const cleanId = imdbId.replace(/^tt/, '');
    const formattedId = `tt${cleanId}`;

    const params = {
      i: formattedId,
      plot: 'full'
    };

    const result = await omdbRequest(params);

    // Get enhanced poster and trailer data from TMDB
    const enhancedData = await getEnhancedMediaData(formattedId);

    const formattedData = formatOMDbData(result, enhancedData);
    const contentType = convertOMDbType(result.Type);

    return {
      success: true,
      data: formattedData,
      rawData: result,
      contentType: contentType,
      imdbId: formattedId
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof OMDbError ? error.message : 'Unknown error occurred',
      statusCode: error instanceof OMDbError ? error.statusCode : undefined
    };
  }
}

// Search OMDb content
async function searchContent(query, type, year, page = 1) {
  try {
    const params = {
      s: query.trim(),
      page: page.toString()
    };

    if (type && type !== 'all') {
      params.type = type;
    }

    if (year) {
      params.y = year;
    }

    const results = await omdbRequest(params);

    return {
      success: true,
      data: results.Search || [],
      total: parseInt(results.totalResults) || 0,
      page: parseInt(page) || 1,
      totalPages: Math.ceil((parseInt(results.totalResults) || 0) / 10)
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof OMDbError ? error.message : 'Unknown error occurred',
      statusCode: error instanceof OMDbError ? error.statusCode : undefined
    };
  }
}

// Validate IMDb ID format
function isValidImdbId(id) {
  return /^tt\d+$/.test(id.toString().trim()) || /^\d+$/.test(id.toString().trim());
}

module.exports = {
  OMDbError,
  getContentByImdbId,
  searchContent,
  formatOMDbData,
  convertOMDbType,
  isValidImdbId
};