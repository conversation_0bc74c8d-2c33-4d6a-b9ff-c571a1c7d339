
import React, { useState, useEffect } from "react";

// Telegram channel link
const telegramLink = "https://t.me/streamdb_online";

// Multiple image sources for maximum compatibility across hosting environments
const getTelegramImageSources = () => {
  const baseUrl = typeof window !== 'undefined' ? window.location.origin : '';
  return [
    "/telegram-banner-new.png",
    "/telegram-banner-backup-new.png",
    "./telegram-banner-new.png",
    `${baseUrl}/telegram-banner-new.png`
  ];
};

export default function TelegramBanner() {
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [imageLoaded, setImageLoaded] = useState(false);
  const [debugInfo, setDebugInfo] = useState<string[]>([]);
  const [telegramImageSources] = useState(() => getTelegramImageSources());

  // Enhanced image loading with comprehensive diagnostics
  useEffect(() => {
    const loadImage = async () => {
      const currentSrc = telegramImageSources[currentImageIndex];
      setDebugInfo(prev => [...prev, `Attempting to load: ${currentSrc}`]);

      try {
        const img = new Image();

        const loadPromise = new Promise<void>((resolve, reject) => {
          const timeout = setTimeout(() => {
            setDebugInfo(prev => [...prev, `⏰ Timeout loading: ${currentSrc}`]);
            reject(new Error(`Timeout loading ${currentSrc}`));
          }, 5000); // 5 second timeout

          img.onload = () => {
            clearTimeout(timeout);
            setDebugInfo(prev => [...prev, `✅ Successfully loaded: ${currentSrc}`]);
            setImageLoaded(true);
            resolve();
          };

          img.onerror = (error) => {
            clearTimeout(timeout);
            setDebugInfo(prev => [...prev, `❌ Failed to load: ${currentSrc} - ${error}`]);
            reject(new Error(`Failed to load ${currentSrc}`));
          };
        });

        img.src = currentSrc;
        await loadPromise;

      } catch (error) {
        if (currentImageIndex < telegramImageSources.length - 1) {
          setDebugInfo(prev => [...prev, `Trying next source...`]);
          setCurrentImageIndex(prev => prev + 1);
        } else {
          setDebugInfo(prev => [...prev, `All sources failed, using last available source`]);
          setImageLoaded(false);
        }
      }
    };

    loadImage();
  }, [currentImageIndex, telegramImageSources]);

  // Log debug info to console for troubleshooting
  useEffect(() => {
    if (debugInfo.length > 0) {
      const latestDebug = debugInfo[debugInfo.length - 1];
      console.log('Telegram Banner Debug:', latestDebug);

      // Also log environment info on first load
      if (debugInfo.length === 1) {
        console.log('Environment Info:', {
          host: typeof window !== 'undefined' ? window.location.host : 'SSR',
          protocol: typeof window !== 'undefined' ? window.location.protocol : 'unknown',
          userAgent: typeof navigator !== 'undefined' ? navigator.userAgent : 'unknown',
          isVercel: typeof window !== 'undefined' ? window.location.host.includes('vercel') : false
        });
      }
    }
  }, [debugInfo]);

  return (
    <section
      className="mb-4 mx-auto max-w-3xl"
      style={{
        background: "none",
        boxShadow: "none",
        border: "none",
        padding: 0,
        transform: "scale(1)",
      }}
    >
      {/* Match Cloudflare banner structure exactly */}
      <div
        className="relative w-full mx-auto overflow-hidden"
        style={{ minHeight: 110, borderRadius: "1.25rem" }}
      >
        <div
          className="relative z-10 px-2 py-5 flex flex-col items-center justify-center"
          style={{
            minHeight: 150, // Match Cloudflare banner's actual content height
          }}
        >
          {/* Clickable area for the entire content */}
          <a
            href={telegramLink}
            target="_blank"
            rel="noopener noreferrer"
            className="block w-full group cursor-pointer focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-background"
            style={{ borderRadius: "1.25rem" }}
            aria-label="Join our Telegram channel for latest uploads, exclusive content, and early access"
            role="button"
            tabIndex={0}
            onKeyDown={(e) => {
              if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                window.open(telegramLink, '_blank', 'noopener,noreferrer');
              }
            }}
          >
            {/* Telegram Banner Content */}
            {(
              <img
                src={telegramImageSources[currentImageIndex]}
                alt="Join our Telegram channel for latest uploads, exclusive content, and early access"
                className="w-full transition-transform duration-200 group-hover:scale-[1.02]"
                style={{
                  height: "150px", // Match content area minHeight for proper card height
                  width: "100%",
                  objectFit: "contain", // Show full image without cropping
                  objectPosition: "center",
                  display: imageLoaded ? "block" : "none",
                  background: "linear-gradient(to right, #5533ff, #337aff)", // Gradient background matching Telegram banner colors
                  borderRadius: "1.25rem", // Ensure rounded corners match container
                }}
                loading="lazy"
                crossOrigin="anonymous"
                onLoad={(e) => {
                  const target = e.target as HTMLImageElement;
                  setImageLoaded(true);
                  setDebugInfo(prev => [...prev, `✅ Image onLoad triggered: ${target.src}`]);
                }}
                onError={(e) => {
                  const target = e.target as HTMLImageElement;
                  setDebugInfo(prev => [...prev, `❌ Image onError triggered: ${target.src}`]);

                  if (currentImageIndex < telegramImageSources.length - 1) {
                    setCurrentImageIndex(prev => prev + 1);
                  } else {
                    setImageLoaded(false);
                  }
                }}
              />
            )}

            
          </a>
        </div>
      </div>
    </section>
  );
}
