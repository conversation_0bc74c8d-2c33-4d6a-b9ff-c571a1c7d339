import { useState, useEffect } from "react";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { useToast } from "@/hooks/use-toast";
import { MediaItem, Season, Episode } from "@/types/media";
import { Search, Play, Calendar, Clock, Users, Filter, Edit, Trash2, Eye, MoreHorizontal, Plus, Settings, Star, Image } from "lucide-react";
import apiService from "@/services/apiService";
import EpisodeManager from "./EpisodeManager";
import ContentEditDialog from "./ContentEditDialog";

interface WebSeriesManagerProps {
  // Props can be added here in the future if needed
}

export default function WebSeriesManager(_props: WebSeriesManagerProps) {
  const { toast } = useToast();
  const [webSeries, setWebSeries] = useState<MediaItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedSeries, setSelectedSeries] = useState<MediaItem | null>(null);
  const [isEpisodeManagerOpen, setIsEpisodeManagerOpen] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalItems, setTotalItems] = useState(0);

  // New state for enhanced CRUD functionality
  const [viewMode, setViewMode] = useState<'grid' | 'table'>('table');
  const [filterStatus, setFilterStatus] = useState<string>('all');
  const [sortBy, setSortBy] = useState<string>('title');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');
  const [selectedItems, setSelectedItems] = useState<string[]>([]);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [editingContent, setEditingContent] = useState<MediaItem | null>(null);

  // Load web series from database
  const loadWebSeries = async (page = 1, search = "") => {
    try {
      setLoading(true);
      const params = {
        type: 'series',
        page,
        limit: 50,
        search: search.trim() || undefined,
        published: undefined // Show both published and unpublished
      };

      const result = await apiService.getContent(params);
      
      if (result.success) {
        setWebSeries(result.data || []);
        setTotalPages(result.pagination?.totalPages || 1);
        setTotalItems(result.pagination?.totalItems || 0);
        setCurrentPage(page);
      } else {
        toast({
          title: "Error",
          description: result.message || "Failed to load web series",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error('Error loading web series:', error);
      toast({
        title: "Error",
        description: "Failed to load web series",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  // Load data on component mount
  useEffect(() => {
    loadWebSeries(1, "");
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  // Handle search
  const handleSearch = (value: string) => {
    setSearchTerm(value);
    setCurrentPage(1);
    loadWebSeries(1, value);
  };

  // Handle page change
  const handlePageChange = (page: number) => {
    loadWebSeries(page, searchTerm);
  };

  // Handle manage episodes
  const handleManageEpisodes = (series: MediaItem) => {
    setSelectedSeries(series);
    setIsEpisodeManagerOpen(true);
  };

  // Handle save content (callback from EpisodeManager)
  const handleSaveContent = (updatedContent: MediaItem) => {
    setWebSeries(prev => prev.map(item =>
      item.id === updatedContent.id ? updatedContent : item
    ));
  };

  // Enhanced CRUD Functions
  const handleEdit = (series: MediaItem) => {
    setEditingContent(series);
    setIsEditDialogOpen(true);
  };

  const handleDelete = async (series: MediaItem) => {
    if (window.confirm(`Are you sure you want to delete "${series.title}"? This will also delete all seasons and episodes.`)) {
      try {
        const result = await apiService.deleteContent(series.id);
        if (result.success) {
          toast({
            title: "Success",
            description: `"${series.title}" has been deleted successfully`,
          });
          loadWebSeries(currentPage, searchTerm);
        } else {
          throw new Error(result.message || 'Failed to delete content');
        }
      } catch (error) {
        console.error('Error deleting content:', error);
        toast({
          title: "Error",
          description: error instanceof Error ? error.message : "Failed to delete content",
          variant: "destructive",
        });
      }
    }
  };

  const handleTogglePublished = async (series: MediaItem) => {
    try {
      const updatedData = { ...series, isPublished: !series.isPublished };
      const result = await apiService.updateContent(series.id, updatedData);

      if (result.success) {
        toast({
          title: "Success",
          description: `"${series.title}" has been ${updatedData.isPublished ? 'published' : 'unpublished'}`,
        });
        loadWebSeries(currentPage, searchTerm);
      } else {
        throw new Error(result.message || 'Failed to update content');
      }
    } catch (error) {
      console.error('Error updating content:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to update content",
        variant: "destructive",
      });
    }
  };

  const handleToggleFeatured = async (series: MediaItem) => {
    try {
      const updatedData = { ...series, isFeatured: !series.isFeatured };
      const result = await apiService.updateContent(series.id, updatedData);

      if (result.success) {
        toast({
          title: "Success",
          description: `"${series.title}" has been ${updatedData.isFeatured ? 'featured' : 'unfeatured'}`,
        });
        loadWebSeries(currentPage, searchTerm);
      } else {
        throw new Error(result.message || 'Failed to update content');
      }
    } catch (error) {
      console.error('Error updating content:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to update content",
        variant: "destructive",
      });
    }
  };

  const handleBulkDelete = async () => {
    if (selectedItems.length === 0) return;

    if (window.confirm(`Are you sure you want to delete ${selectedItems.length} selected web series? This action cannot be undone.`)) {
      try {
        const result = await apiService.bulkContentOperation('delete', selectedItems);
        if (result.success) {
          toast({
            title: "Success",
            description: `${selectedItems.length} web series deleted successfully`,
          });
          setSelectedItems([]);
          loadWebSeries(currentPage, searchTerm);
        } else {
          throw new Error(result.message || 'Failed to delete content');
        }
      } catch (error) {
        console.error('Error deleting content:', error);
        toast({
          title: "Error",
          description: error instanceof Error ? error.message : "Failed to delete content",
          variant: "destructive",
        });
      }
    }
  };

  const handleSelectAll = () => {
    if (selectedItems.length === webSeries.length) {
      setSelectedItems([]);
    } else {
      setSelectedItems(webSeries.map(item => item.id));
    }
  };

  const handleSelectItem = (id: string) => {
    setSelectedItems(prev =>
      prev.includes(id)
        ? prev.filter(item => item !== id)
        : [...prev, id]
    );
  };

  // Filter and sort web series
  const filteredAndSortedSeries = webSeries
    .filter(series => {
      if (filterStatus === 'published') return series.isPublished;
      if (filterStatus === 'draft') return !series.isPublished;
      if (filterStatus === 'featured') return series.isFeatured;
      return true;
    })
    .sort((a, b) => {
      let aValue = a[sortBy as keyof MediaItem];
      let bValue = b[sortBy as keyof MediaItem];

      if (typeof aValue === 'string') aValue = aValue.toLowerCase();
      if (typeof bValue === 'string') bValue = bValue.toLowerCase();

      if (sortOrder === 'asc') {
        return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
      } else {
        return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
      }
    });

  if (loading && webSeries.length === 0) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="text-4xl mb-4">📺</div>
          <p className="text-lg text-muted-foreground">Loading web series...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Play className="w-5 h-5 text-primary" />
            All Web Series Management
          </CardTitle>
          <p className="text-sm text-muted-foreground">
            Complete CRUD operations for web series, seasons, and episodes. Full content management with advanced filtering and bulk operations.
          </p>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col lg:flex-row gap-4 items-start lg:items-center justify-between">
            {/* Search and Filters */}
            <div className="flex flex-col sm:flex-row gap-4 flex-1">
              <div className="relative flex-1 max-w-md">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                <Input
                  placeholder="Search web series..."
                  value={searchTerm}
                  onChange={(e) => handleSearch(e.target.value)}
                  className="pl-10"
                />
              </div>

              <div className="flex gap-2">
                <Select value={filterStatus} onValueChange={setFilterStatus}>
                  <SelectTrigger className="w-32">
                    <SelectValue placeholder="Status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Status</SelectItem>
                    <SelectItem value="published">Published</SelectItem>
                    <SelectItem value="draft">Draft</SelectItem>
                    <SelectItem value="featured">Featured</SelectItem>
                  </SelectContent>
                </Select>

                <Select value={sortBy} onValueChange={setSortBy}>
                  <SelectTrigger className="w-32">
                    <SelectValue placeholder="Sort by" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="title">Title</SelectItem>
                    <SelectItem value="year">Year</SelectItem>
                    <SelectItem value="totalSeasons">Seasons</SelectItem>
                    <SelectItem value="totalEpisodes">Episodes</SelectItem>
                    <SelectItem value="createdAt">Created</SelectItem>
                  </SelectContent>
                </Select>

                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}
                >
                  {sortOrder === 'asc' ? '↑' : '↓'}
                </Button>
              </div>
            </div>

            {/* Actions and Stats */}
            <div className="flex items-center gap-4">
              {selectedItems.length > 0 && (
                <div className="flex items-center gap-2">
                  <span className="text-sm text-muted-foreground">
                    {selectedItems.length} selected
                  </span>
                  <Button
                    variant="destructive"
                    size="sm"
                    onClick={handleBulkDelete}
                  >
                    <Trash2 className="w-4 h-4 mr-1" />
                    Delete
                  </Button>
                </div>
              )}

              <div className="flex items-center gap-4 text-sm text-muted-foreground">
                <div className="flex items-center gap-1">
                  <Users className="w-4 h-4" />
                  <span>{totalItems} series</span>
                </div>
                <div className="flex items-center gap-1">
                  <Filter className="w-4 h-4" />
                  <span>Page {currentPage} of {totalPages}</span>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Web Series Table */}
      <Card>
        <CardContent className="p-0">
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-12">
                    <input
                      type="checkbox"
                      checked={selectedItems.length === webSeries.length && webSeries.length > 0}
                      onChange={handleSelectAll}
                      className="rounded"
                    />
                  </TableHead>
                  <TableHead className="w-16 hidden sm:table-cell">Image</TableHead>
                  <TableHead className="min-w-[200px]">Title</TableHead>
                  <TableHead className="w-20 hidden md:table-cell">Year</TableHead>
                  <TableHead className="w-24 hidden lg:table-cell">Seasons</TableHead>
                  <TableHead className="w-24 hidden lg:table-cell">Episodes</TableHead>
                  <TableHead className="w-24">Status</TableHead>
                  <TableHead className="w-20 hidden md:table-cell">Featured</TableHead>
                  <TableHead className="w-32">Actions</TableHead>
                </TableRow>
              </TableHeader>
            <TableBody>
              {filteredAndSortedSeries.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={9} className="text-center py-8">
                    <div className="text-center">
                      <div className="text-4xl mb-4">📺</div>
                      <p className="text-lg text-muted-foreground">No web series found</p>
                      <p className="text-sm text-muted-foreground mt-1">
                        {searchTerm ? 'Try adjusting your search terms' : 'Add your first web series to get started'}
                      </p>
                    </div>
                  </TableCell>
                </TableRow>
              ) : (
                filteredAndSortedSeries.map((series) => (
                  <TableRow key={series.id} className="hover:bg-muted/50">
                    <TableCell>
                      <input
                        type="checkbox"
                        checked={selectedItems.includes(series.id)}
                        onChange={() => handleSelectItem(series.id)}
                        className="rounded"
                      />
                    </TableCell>
                    <TableCell className="hidden sm:table-cell">
                      <div className="w-12 h-8 rounded overflow-hidden bg-muted">
                        <img
                          src={series.image || series.posterUrl || '/placeholder-image.jpg'}
                          alt={series.title}
                          className="w-full h-full object-cover"
                        />
                      </div>
                    </TableCell>
                    <TableCell>
                      <div>
                        <div className="font-medium line-clamp-1">{series.title}</div>
                        {/* Mobile-only metadata */}
                        <div className="flex items-center gap-4 text-xs text-muted-foreground mt-1 md:hidden">
                          <span>{series.year}</span>
                          <div className="flex items-center gap-1 lg:hidden">
                            <Play className="w-3 h-3" />
                            <span>{series.totalSeasons || 0}S</span>
                          </div>
                          <div className="flex items-center gap-1 lg:hidden">
                            <Clock className="w-3 h-3" />
                            <span>{series.totalEpisodes || 0}E</span>
                          </div>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell className="hidden md:table-cell">{series.year}</TableCell>
                    <TableCell className="hidden lg:table-cell">
                      <div className="flex items-center gap-1">
                        <Play className="w-3 h-3" />
                        <span>{series.totalSeasons || 0}</span>
                      </div>
                    </TableCell>
                    <TableCell className="hidden lg:table-cell">
                      <div className="flex items-center gap-1">
                        <Clock className="w-3 h-3" />
                        <span>{series.totalEpisodes || 0}</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant={series.isPublished ? "default" : "secondary"} className="text-xs">
                        {series.isPublished ? "Published" : "Draft"}
                      </Badge>
                    </TableCell>
                    <TableCell className="hidden md:table-cell">
                      <div className="flex items-center justify-center">
                        {series.isFeatured ? (
                          <Star className="w-4 h-4 text-yellow-500 fill-current" />
                        ) : (
                          <Star className="w-4 h-4 text-muted-foreground" />
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-1">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleManageEpisodes(series)}
                          className="h-8 w-8 p-0"
                        >
                          <Play className="w-4 h-4" />
                        </Button>

                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                              <MoreHorizontal className="w-4 h-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem onClick={() => handleEdit(series)}>
                              <Edit className="w-4 h-4 mr-2" />
                              Edit
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => handleTogglePublished(series)}>
                              <Eye className="w-4 h-4 mr-2" />
                              {series.isPublished ? 'Unpublish' : 'Publish'}
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => handleToggleFeatured(series)}>
                              <Star className="w-4 h-4 mr-2" />
                              {series.isFeatured ? 'Unfeature' : 'Feature'}
                            </DropdownMenuItem>
                            <DropdownMenuItem
                              onClick={() => handleDelete(series)}
                              className="text-destructive"
                            >
                              <Trash2 className="w-4 h-4 mr-2" />
                              Delete
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
          </div>
        </CardContent>
      </Card>

      {/* Empty State */}
      {webSeries.length === 0 && !loading && (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-12">
            <div className="text-6xl mb-4">📺</div>
            <h3 className="text-xl font-semibold mb-2">No Web Series Found</h3>
            <p className="text-muted-foreground text-center max-w-md">
              {searchTerm 
                ? `No web series found matching "${searchTerm}". Try adjusting your search terms.`
                : "No web series have been added yet. Create some web series content first."
              }
            </p>
          </CardContent>
        </Card>
      )}

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex justify-center gap-2">
          <Button
            variant="outline"
            onClick={() => handlePageChange(currentPage - 1)}
            disabled={currentPage === 1}
          >
            Previous
          </Button>
          <span className="flex items-center px-4 text-sm text-muted-foreground">
            Page {currentPage} of {totalPages}
          </span>
          <Button
            variant="outline"
            onClick={() => handlePageChange(currentPage + 1)}
            disabled={currentPage === totalPages}
          >
            Next
          </Button>
        </div>
      )}

      {/* Episode Manager Dialog */}
      <EpisodeManager
        isOpen={isEpisodeManagerOpen}
        onClose={() => setIsEpisodeManagerOpen(false)}
        content={selectedSeries}
        onSave={handleSaveContent}
      />

      {/* Content Edit Dialog */}
      <ContentEditDialog
        isOpen={isEditDialogOpen}
        onClose={() => {
          setIsEditDialogOpen(false);
          setEditingContent(null);
        }}
        content={editingContent}
        onSave={(updatedContent) => {
          setWebSeries(prev => prev.map(item =>
            item.id === updatedContent.id ? updatedContent : item
          ));
          setIsEditDialogOpen(false);
          setEditingContent(null);
          toast({
            title: "Success",
            description: "Web series updated successfully",
          });
        }}
      />
    </div>
  );
}
