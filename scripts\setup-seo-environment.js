#!/usr/bin/env node

/**
 * SEO Environment Setup Script
 * Ensures all dependencies and configurations are properly set up for automatic SEO updates
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log('🔧 Setting up SEO environment for StreamDB...');

// Check if mysql2 is installed in root directory
function checkMysql2Installation() {
  const packageJsonPath = path.join(__dirname, '..', 'package.json');
  const nodeModulesPath = path.join(__dirname, '..', 'node_modules', 'mysql2');
  
  try {
    const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
    const hasMysql2Dependency = packageJson.dependencies && packageJson.dependencies.mysql2;
    const hasMysql2Installed = fs.existsSync(nodeModulesPath);
    
    if (hasMysql2Dependency && hasMysql2Installed) {
      console.log('✅ mysql2 is properly installed in root directory');
      return true;
    } else {
      console.log('❌ mysql2 is not installed in root directory');
      console.log('   Run: npm install mysql2');
      return false;
    }
  } catch (error) {
    console.log('❌ Could not check mysql2 installation:', error.message);
    return false;
  }
}

// Check if root environment file exists
function checkRootEnvironment() {
  const rootEnvPath = path.join(__dirname, '..', '.env');

  if (fs.existsSync(rootEnvPath)) {
    console.log('✅ Root environment file found');

    // Check for required database variables
    const envContent = fs.readFileSync(rootEnvPath, 'utf8');
    const requiredVars = ['DB_HOST', 'DB_USER', 'DB_PASSWORD', 'DB_NAME'];
    const missingVars = [];

    for (const varName of requiredVars) {
      if (!envContent.includes(`${varName}=`)) {
        missingVars.push(varName);
      }
    }

    if (missingVars.length === 0) {
      console.log('✅ All required database environment variables found');
      return true;
    } else {
      console.log('❌ Missing database environment variables:', missingVars.join(', '));
      return false;
    }
  } else {
    console.log('❌ Root environment file not found at .env');
    return false;
  }
}

// Check if logs directory exists
function checkLogsDirectory() {
  const logsDir = path.join(__dirname, '..', 'logs');
  
  if (!fs.existsSync(logsDir)) {
    console.log('📁 Creating logs directory...');
    fs.mkdirSync(logsDir, { recursive: true });
  }
  
  console.log('✅ Logs directory is ready');
  return true;
}

// Test socket-based database connection (production method)
async function testSocketDatabaseConnection() {
  try {
    console.log('🔗 Testing socket-based database connection (production method)...');

    const { testAllSocketConnections, executeQuery } = await import('./database-connection-handler.js');

    // Test all socket connection methods
    const results = await testAllSocketConnections();

    // Check if any socket method worked
    const workingMethods = results.filter(r => r.success);

    if (workingMethods.length === 0) {
      console.log('❌ No socket connection methods worked');
      console.log('💡 Check MySQL socket file permissions: ls -la /var/run/mysqld/mysqld.sock');
      return false;
    }

    console.log(`✅ Found ${workingMethods.length} working socket connection method(s)`);

    // Test actual query execution
    const content = await executeQuery('SELECT COUNT(*) as count FROM content WHERE is_published = 1');
    console.log(`✅ Socket database query successful - Found ${content[0].count} published content items`);
    console.log('✓ SEO scripts now use the same connection method as production server');

    return true;

  } catch (error) {
    console.log('❌ Socket database connection test failed:', error.message);
    console.log('💡 Ensure MySQL socket is accessible and has proper permissions');
    return false;
  }
}

// Test sitemap generation
async function testSitemapGeneration() {
  try {
    console.log('🗺️ Testing sitemap generation...');
    
    const { generateSitemap } = await import('./generate-sitemap.js');
    await generateSitemap();
    
    console.log('✅ Sitemap generation test successful');
    return true;
    
  } catch (error) {
    console.log('❌ Sitemap generation test failed:', error.message);
    return false;
  }
}

// Main setup function
async function setupEnvironment() {
  console.log('🚀 Starting SEO environment setup...\n');
  
  const checks = [
    { name: 'MySQL2 Installation', fn: checkMysql2Installation },
    { name: 'Root Environment', fn: checkRootEnvironment },
    { name: 'Logs Directory', fn: checkLogsDirectory },
    { name: 'Socket Database Connection', fn: testSocketDatabaseConnection },
    { name: 'Sitemap Generation', fn: testSitemapGeneration }
  ];
  
  let allPassed = true;
  
  for (const check of checks) {
    try {
      const result = await check.fn();
      if (!result) {
        allPassed = false;
      }
    } catch (error) {
      console.log(`❌ ${check.name} failed:`, error.message);
      allPassed = false;
    }
    console.log(''); // Add spacing
  }
  
  if (allPassed) {
    console.log('🎉 All checks passed! SEO environment is ready.');
    console.log('\n📋 Next steps:');
    console.log('1. Update your crontab with the corrected command');
    console.log('2. Test manual SEO update: npm run auto-seo-update');
    console.log('3. Monitor logs in logs/seo-updates.log');
  } else {
    console.log('⚠️ Some checks failed. Please fix the issues above before proceeding.');
  }
  
  return allPassed;
}

// Run setup if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  setupEnvironment().catch(console.error);
}

export { setupEnvironment };
