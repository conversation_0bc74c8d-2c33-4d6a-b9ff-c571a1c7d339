import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { useToast } from "@/hooks/use-toast";
// Note: Drag and drop functionality can be added later with @hello-pangea/dnd
import { 
  Plus, 
  Trash2, 
  Eye, 
  Settings, 
  ArrowUp, 
  ArrowDown, 
  GripVertical,
  Image as ImageIcon,
  Play,
  Edit,
  Save,
  X,
  Search,
  Filter,
  RefreshCw
} from "lucide-react";
import apiService from "@/services/apiService";
import { MediaItem } from "@/types/media";
import { getHomepageContent } from "@/utils/contentFilters";

interface CarouselItem extends MediaItem {
  carouselPosition?: number;
  cropSettings?: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
}

interface CropSettings {
  x: number;
  y: number;
  width: number;
  height: number;
}

export default function HeroCarouselManager() {
  const { toast } = useToast();
  
  // State management
  const [carouselItems, setCarouselItems] = useState<CarouselItem[]>([]);
  const [availableContent, setAvailableContent] = useState<MediaItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [filterType, setFilterType] = useState<"all" | "movie" | "series">("all");
  const [selectedItem, setSelectedItem] = useState<CarouselItem | null>(null);
  const [isCropDialogOpen, setIsCropDialogOpen] = useState(false);
  const [isAddContentDialogOpen, setIsAddContentDialogOpen] = useState(false);
  const [previewMode, setPreviewMode] = useState(false);
  const [queuedItems, setQueuedItems] = useState<CarouselItem[]>([]);

  // Load carousel and available content
  const loadData = async () => {
    setIsLoading(true);
    try {
      // Get ALL content from database (not just carousel items) for Add Content functionality
      const response = await apiService.getContent({ limit: 1000 });
      if (response.success) {
        const allContent = response.data;
        
        // Filter items that are specifically marked for carousel (using snake_case field name from database)
        const carouselContent = allContent.filter(item => item.add_to_carousel === true || item.add_to_carousel === 1);
        
        // Sort all carousel items by position
        const allSortedItems = carouselContent
          .sort((a, b) => (a.carousel_position || 999) - (b.carousel_position || 999))
          .map((item, index) => ({
            ...item,
            carouselPosition: index + 1,
            cropSettings: (() => {
              try {
                // Handle different crop_settings formats
                if (!item.crop_settings) {
                  return { x: 50, y: 50, width: 100, height: 100 };
                }
                
                // If it's already an object, return it
                if (typeof item.crop_settings === 'object') {
                  return item.crop_settings;
                }
                
                // If it's a string, try to parse it
                if (typeof item.crop_settings === 'string') {
                  return JSON.parse(item.crop_settings);
                }
                
                // Fallback to default
                return { x: 50, y: 50, width: 100, height: 100 };
              } catch (error) {
                console.warn(`Failed to parse crop_settings for item ${item.id}:`, error);
                return { x: 50, y: 50, width: 100, height: 100 };
              }
            })()
          }));

        // Split into active carousel items (first 10) and queue items (beyond 10)
        const activeItems = allSortedItems.slice(0, 10);
        const queueItems = allSortedItems.slice(10);
        
        setCarouselItems(activeItems);
        setQueuedItems(queueItems);
        
        // Available content excludes ALL items in carousel (active + queue)
        const carouselIds = new Set(allSortedItems.map(item => item.id));
        const available = allContent.filter(item => 
          !carouselIds.has(item.id) && (item.is_published === true || item.is_published === 1)
        );
        setAvailableContent(available);
      }
    } catch (error) {
      console.error('Error loading carousel data:', error);
      toast({
        title: "Error",
        description: "Failed to load carousel data",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    loadData();
  }, []);

  // Handle reordering with up/down buttons
  const moveItem = (index: number, direction: 'up' | 'down') => {
    const items = Array.from(carouselItems);
    const targetIndex = direction === 'up' ? index - 1 : index + 1;
    
    if (targetIndex < 0 || targetIndex >= items.length) return;
    
    // Swap items
    [items[index], items[targetIndex]] = [items[targetIndex], items[index]];
    
    // Update positions
    const updatedItems = items.map((item, idx) => ({
      ...item,
      carouselPosition: idx + 1
    }));

    setCarouselItems(updatedItems);
  };

  // Remove item from carousel
  const removeFromCarousel = async (itemId: string) => {
    try {
      setIsSaving(true);
      
      // Update the item to remove from carousel using the correct method
      await apiService.removeFromCarousel(itemId);
      
      // Move item from carousel to available content
      const removedItem = carouselItems.find(item => item.id === itemId);
      if (removedItem) {
        setCarouselItems(prev => prev.filter(item => item.id !== itemId));
        setAvailableContent(prev => [...prev, removedItem]);
      }

      toast({
        title: "Success",
        description: "Item removed from Hero Carousel",
      });
    } catch (error) {
      console.error('Error removing from carousel:', error);
      toast({
        title: "Error",
        description: "Failed to remove item from carousel",
        variant: "destructive"
      });
    } finally {
      setIsSaving(false);
    }
  };

  // Add item to carousel
  const addToCarousel = async (item: MediaItem) => {
    if (carouselItems.length >= 10) {
      toast({
        title: "Carousel Full",
        description: "Hero Carousel can only contain 10 items. Remove an item first.",
        variant: "destructive"
      });
      return;
    }

    try {
      setIsSaving(true);
      
      // Update the item to add to carousel using the correct method
      await apiService.addToCarousel(item.id, carouselItems.length + 1);
      
      // Move item from available to carousel
      const newCarouselItem: CarouselItem = {
        ...item,
        carouselPosition: carouselItems.length + 1,
        cropSettings: { x: 0, y: 0, width: 100, height: 100 }
      };
      
      setCarouselItems(prev => [...prev, newCarouselItem]);
      setAvailableContent(prev => prev.filter(availableItem => availableItem.id !== item.id));
      setIsAddContentDialogOpen(false);

      toast({
        title: "Success",
        description: "Item added to Hero Carousel",
      });
    } catch (error) {
      console.error('Error adding to carousel:', error);
      toast({
        title: "Error",
        description: "Failed to add item to carousel",
        variant: "destructive"
      });
    } finally {
      setIsSaving(false);
    }
  };

  // Save carousel order
  const saveCarouselOrder = async () => {
    try {
      setIsSaving(true);
      
      // Combine active and queued items for saving
      const allItems = [...carouselItems, ...queuedItems];
      
      // Use the dedicated carousel reorder endpoint with proper format
      const carouselItemsData = {
        carouselItems: allItems.map((item, index) => ({
          id: item.id,
          position: index + 1
        }))
      };
      
      await apiService.updateCarouselOrder(carouselItemsData);

      toast({
        title: "Success",
        description: "Carousel order saved successfully",
      });
    } catch (error) {
      console.error('Error saving carousel order:', error);
      toast({
        title: "Error",
        description: "Failed to save carousel order",
        variant: "destructive"
      });
    } finally {
      setIsSaving(false);
    }
  };

  // Update crop settings
  const updateCropSettings = async (itemId: string, cropSettings: CropSettings) => {
    try {
      setIsSaving(true);
      
      // Use the correct API endpoint for crop settings
      await apiService.updateCarouselCropSettings(itemId, cropSettings);
      
      setCarouselItems(prev => prev.map(item => 
        item.id === itemId ? { ...item, cropSettings } : item
      ));

      toast({
        title: "Success",
        description: "Crop settings updated",
      });
    } catch (error) {
      console.error('Error updating crop settings:', error);
      toast({
        title: "Error",
        description: "Failed to update crop settings",
        variant: "destructive"
      });
    } finally {
      setIsSaving(false);
    }
  };

  // Filter available content
  const filteredAvailableContent = availableContent.filter(item => {
    const matchesSearch = item.title.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesType = filterType === "all" || item.type === filterType;
    return matchesSearch && matchesType;
  });

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <RefreshCw className="h-8 w-8 animate-spin" />
        <span className="ml-2">Loading Hero Carousel data...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-white">Hero Carousel Manager</h2>
          <p className="text-gray-400">
            Manage the homepage Hero Carousel content and display settings
          </p>
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={() => setPreviewMode(!previewMode)}
            className="border-gray-600 text-gray-300 hover:bg-gray-700"
          >
            <Eye className="h-4 w-4 mr-2" />
            {previewMode ? "Edit Mode" : "Preview Mode"}
          </Button>
          <Button
            onClick={saveCarouselOrder}
            disabled={isSaving}
            className="bg-blue-600 hover:bg-blue-700"
          >
            <Save className="h-4 w-4 mr-2" />
            Save Changes
          </Button>
        </div>
      </div>

      {/* Carousel Status */}
      <Card className="bg-gray-900 border-gray-700">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-white">
            <Play className="h-5 w-5" />
            Carousel Status
          </CardTitle>
          <CardDescription className="text-gray-400">
            Current Hero Carousel contains {carouselItems.length} of 10 maximum items
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-4">
            <div className="flex-1 bg-gray-200 rounded-full h-2">
              <div 
                className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                style={{ width: `${(carouselItems.length / 10) * 100}%` }}
              />
            </div>
            <Badge variant={carouselItems.length === 10 ? "destructive" : "default"}>
              {carouselItems.length}/10
            </Badge>
          </div>
        </CardContent>
      </Card>

      {/* Current Carousel Items */}
      <Card className="bg-gray-900 border-gray-700">
        <CardHeader>
          <CardTitle className="flex items-center justify-between text-white">
            <span className="flex items-center gap-2">
              <GripVertical className="h-5 w-5" />
              Active Carousel Items <Badge className="ml-2 bg-blue-600">{carouselItems.length}/10</Badge>
            </span>
            <Button
              onClick={() => setIsAddContentDialogOpen(true)}
              disabled={carouselItems.length >= 10}
              size="sm"
              className="bg-blue-600 hover:bg-blue-700"
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Content
            </Button>
          </CardTitle>
          <CardDescription className="text-gray-400">
            Only the first 10 items will be shown in the Hero Carousel. Drag and drop to reorder.
          </CardDescription>
        </CardHeader>
        <CardContent>
          {carouselItems.length === 0 ? (
            <div className="text-center py-8 text-gray-400">
              <ImageIcon className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>No items in Hero Carousel</p>
              <p className="text-sm">Add content to get started</p>
            </div>
          ) : (
            <div className="space-y-4">
              {carouselItems.map((item, index) => (
                <div
                  key={item.id}
                  className="flex items-center gap-4 p-4 border border-gray-600 rounded-lg bg-gray-800 hover:bg-gray-750 transition-all"
                >
                  {/* Reorder Controls */}
                  <div className="flex flex-col gap-1">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => moveItem(index, 'up')}
                      disabled={index === 0 || isSaving}
                      className="h-6 w-6 p-0"
                    >
                      <ArrowUp className="h-3 w-3" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => moveItem(index, 'down')}
                      disabled={index === carouselItems.length - 1 || isSaving}
                      className="h-6 w-6 p-0"
                    >
                      <ArrowDown className="h-3 w-3" />
                    </Button>
                  </div>
                  
                  <div className="flex items-center gap-4 flex-1">
                    <div className="relative w-16 h-24 bg-gray-200 rounded overflow-hidden">
                      <img
                        src={item.image || item.posterUrl}
                        alt={item.title}
                        className="w-full h-full object-cover"
                        onError={(e) => {
                          e.currentTarget.src = "/placeholder-poster.jpg";
                        }}
                      />
                      <div className="absolute top-1 left-1 bg-blue-600 text-white text-xs px-1 rounded">
                        {index + 1}
                      </div>
                    </div>
                    
                    <div className="flex-1">
                      <h3 className="font-semibold">{item.title}</h3>
                      <p className="text-sm text-muted-foreground">
                        {item.year} • {item.type} • {item.genres?.join(", ")}
                      </p>
                      <div className="flex gap-2 mt-2">
                        <Badge variant="outline">{item.type}</Badge>
                        {item.isFeatured && <Badge variant="secondary">Featured</Badge>}
                      </div>
                    </div>
                  </div>

                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        setSelectedItem(item);
                        setIsCropDialogOpen(true);
                      }}
                    >
                      <Settings className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => removeFromCarousel(item.id)}
                      disabled={isSaving}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Queue Items Section */}
      {queuedItems.length > 0 && (
        <Card className="bg-gray-900 border-gray-700 border-dashed">
          <CardHeader>
            <CardTitle className="flex items-center justify-between text-white">
              <span className="flex items-center gap-2">
                <ImageIcon className="h-5 w-5" />
                Queue Items <Badge className="ml-2 bg-amber-600">{queuedItems.length}</Badge>
              </span>
            </CardTitle>
            <CardDescription className="text-gray-400">
              These items are in the queue but not shown in the carousel. They will be shown when active items are removed.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {queuedItems.map((item, index) => (
                <div
                  key={item.id}
                  className="flex items-center gap-4 p-4 border border-gray-600 rounded-lg bg-gray-800 hover:bg-gray-750 transition-all opacity-80"
                >
                  <div className="flex items-center gap-4 flex-1">
                    <div className="relative w-16 h-24 bg-gray-200 rounded overflow-hidden">
                      <img
                        src={item.image || item.posterUrl}
                        alt={item.title}
                        className="w-full h-full object-cover"
                        onError={(e) => {
                          e.currentTarget.src = "/placeholder-poster.jpg";
                        }}
                      />
                      <div className="absolute top-1 left-1 bg-amber-600 text-white text-xs px-1 rounded">
                        Queue #{index + 1}
                      </div>
                    </div>
                    
                    <div className="flex-1">
                      <h3 className="font-semibold">{item.title}</h3>
                      <p className="text-sm text-muted-foreground">
                        {item.year} • {item.type} • {item.genres?.join(", ")}
                      </p>
                    </div>
                  </div>

                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => removeFromCarousel(item.id)}
                      disabled={isSaving}
                      className="border-gray-600 text-gray-300 hover:bg-gray-700"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Add Content Dialog */}
      <Dialog open={isAddContentDialogOpen} onOpenChange={setIsAddContentDialogOpen}>
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Add Content to Hero Carousel</DialogTitle>
          </DialogHeader>
          
          <div className="space-y-4">
            {/* Search and Filter */}
            <div className="flex gap-4">
              <div className="flex-1">
                <Label htmlFor="search">Search Content</Label>
                <div className="relative">
                  <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                  <Input
                    id="search"
                    placeholder="Search by title..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              <div>
                <Label htmlFor="filter">Filter by Type</Label>
                <select
                  id="filter"
                  value={filterType}
                  onChange={(e) => setFilterType(e.target.value as "all" | "movie" | "series")}
                  className="w-full p-2 border rounded-md"
                >
                  <option value="all">All Types</option>
                  <option value="movie">Movies</option>
                  <option value="series">Series</option>
                </select>
              </div>
            </div>

            {/* Available Content Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 max-h-96 overflow-y-auto">
              {filteredAvailableContent.map((item) => (
                <div key={item.id} className="border rounded-lg p-4 hover:shadow-md transition-shadow">
                  <div className="flex gap-3">
                    <div className="w-12 h-16 bg-gray-200 rounded overflow-hidden flex-shrink-0">
                      <img
                        src={item.image || item.posterUrl}
                        alt={item.title}
                        className="w-full h-full object-cover"
                        onError={(e) => {
                          e.currentTarget.src = "/placeholder-poster.jpg";
                        }}
                      />
                    </div>
                    <div className="flex-1 min-w-0">
                      <h4 className="font-medium truncate">{item.title}</h4>
                      <p className="text-sm text-muted-foreground">
                        {item.year} • {item.type}
                      </p>
                      <div className="flex gap-1 mt-2">
                        <Badge variant="outline" className="text-xs">
                          {item.type}
                        </Badge>
                      </div>
                      <Button
                        size="sm"
                        className="mt-2 w-full"
                        onClick={() => addToCarousel(item)}
                        disabled={isSaving}
                      >
                        <Plus className="h-3 w-3 mr-1" />
                        Add
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {filteredAvailableContent.length === 0 && (
              <div className="text-center py-8 text-muted-foreground">
                <Search className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>No content found</p>
                <p className="text-sm">Try adjusting your search or filter</p>
              </div>
            )}
          </div>
        </DialogContent>
      </Dialog>

      {/* Crop Settings Dialog */}
      <Dialog open={isCropDialogOpen} onOpenChange={setIsCropDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Poster Crop Settings</DialogTitle>
          </DialogHeader>
          
          {selectedItem && (
            <div className="space-y-4">
              <div className="text-sm text-muted-foreground">
                Adjust how the poster image appears in the Hero Carousel
              </div>
              
              {/* Image Preview */}
              <div className="relative w-full h-64 bg-gray-200 rounded overflow-hidden">
                <img
                  src={selectedItem.image || selectedItem.posterUrl}
                  alt={selectedItem.title}
                  className="w-full h-full object-cover"
                  style={{
                    objectPosition: `${selectedItem.cropSettings?.x || 50}% ${selectedItem.cropSettings?.y || 50}%`
                  }}
                />
                <div className="absolute top-2 right-2 bg-black bg-opacity-50 text-white text-xs px-2 py-1 rounded">
                  Preview: {selectedItem.cropSettings?.x || 50}% / {selectedItem.cropSettings?.y || 50}%
                </div>
              </div>

              {/* Crop Controls */}
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="crop-x">Horizontal Position (%)</Label>
                  <Input
                    id="crop-x"
                    type="number"
                    min="0"
                    max="100"
                    step="5"
                    value={selectedItem.cropSettings?.x || 50}
                    onChange={(e) => {
                      const newCropSettings = {
                        x: parseInt(e.target.value) || 50,
                        y: selectedItem.cropSettings?.y || 50,
                        width: selectedItem.cropSettings?.width || 100,
                        height: selectedItem.cropSettings?.height || 100
                      };
                      setSelectedItem({
                        ...selectedItem,
                        cropSettings: newCropSettings
                      });
                    }}
                  />
                  <div className="text-xs text-muted-foreground mt-1">
                    0% = Left, 50% = Center, 100% = Right
                  </div>
                </div>
                <div>
                  <Label htmlFor="crop-y">Vertical Position (%)</Label>
                  <Input
                    id="crop-y"
                    type="number"
                    min="0"
                    max="100"
                    step="5"
                    value={selectedItem.cropSettings?.y || 50}
                    onChange={(e) => {
                      const newCropSettings = {
                        x: selectedItem.cropSettings?.x || 50,
                        y: parseInt(e.target.value) || 50,
                        width: selectedItem.cropSettings?.width || 100,
                        height: selectedItem.cropSettings?.height || 100
                      };
                      setSelectedItem({
                        ...selectedItem,
                        cropSettings: newCropSettings
                      });
                    }}
                  />
                  <div className="text-xs text-muted-foreground mt-1">
                    0% = Top, 50% = Center, 100% = Bottom
                  </div>
                </div>
              </div>

              {/* Quick Preset Buttons */}
              <div className="space-y-2">
                <Label>Quick Presets</Label>
                <div className="grid grid-cols-3 gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      const newCropSettings = { x: 50, y: 50, width: 100, height: 100 };
                      setSelectedItem({ ...selectedItem, cropSettings: newCropSettings });
                    }}
                  >
                    Center
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      const newCropSettings = { x: 50, y: 25, width: 100, height: 100 };
                      setSelectedItem({ ...selectedItem, cropSettings: newCropSettings });
                    }}
                  >
                    Top Center
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      const newCropSettings = { x: 50, y: 75, width: 100, height: 100 };
                      setSelectedItem({ ...selectedItem, cropSettings: newCropSettings });
                    }}
                  >
                    Bottom Center
                  </Button>
                </div>
              </div>

              <div className="flex justify-end gap-2">
                <Button
                  variant="outline"
                  onClick={() => setIsCropDialogOpen(false)}
                >
                  Cancel
                </Button>
                <Button
                  onClick={() => {
                    if (selectedItem.cropSettings) {
                      updateCropSettings(selectedItem.id, selectedItem.cropSettings);
                    }
                    setIsCropDialogOpen(false);
                  }}
                  disabled={isSaving}
                >
                  <Save className="h-4 w-4 mr-2" />
                  Save Settings
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}