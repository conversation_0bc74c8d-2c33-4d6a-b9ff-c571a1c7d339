import { MediaItem } from '@/types/media';

export interface CSVParseResult {
  data: Partial<MediaItem>[];
  errors: string[];
  warnings: string[];
  totalRows: number;
  validRows: number;
}

export interface CSVValidationError {
  row: number;
  field: string;
  message: string;
}

// Expected CSV headers in order
export const CSV_HEADERS = [
  'Title', 'Description', 'Type', 'Category', 'Section IDs', 'Year', 'Genres', 'Languages', 'Status',
  'Featured', 'Carousel', 'IMDb Rating', 'Runtime', 'Studio', 'Tags',
  'Poster URL', 'Thumbnail URL', 'Cover Image', 'Trailer URL', 'Subtitle URL',
  'Video Links', 'Secure Video Links', 'Quality', 'Quality Label', 'Custom Quality Label', 'Audio Tracks',
  'TMDB ID', 'Total Seasons', 'Total Episodes', 'Created At', 'Updated At'
];

// Required fields for validation - Based on user requirements, only Title is mandatory for main content
// Season Title, Episode Title, and Episode Video Embed Links are handled separately for web series
const REQUIRED_FIELDS = ['Title'];

/**
 * Parse CSV text content into structured data
 */
export function parseCSV(csvContent: string): CSVParseResult {
  const lines = csvContent.split('\n').filter(line => line.trim());
  const errors: string[] = [];
  const warnings: string[] = [];
  const data: Partial<MediaItem>[] = [];

  if (lines.length === 0) {
    errors.push('CSV file is empty');
    return { data, errors, warnings, totalRows: 0, validRows: 0 };
  }

  // Parse header row
  const headerRow = parseCSVRow(lines[0]);
  const headerMap = createHeaderMap(headerRow);

  // Validate headers
  const missingRequired = REQUIRED_FIELDS.filter(field => !headerMap.has(field.toLowerCase()));
  if (missingRequired.length > 0) {
    errors.push(`Missing required headers: ${missingRequired.join(', ')}`);
  }

  // Parse data rows
  for (let i = 1; i < lines.length; i++) {
    const rowNumber = i + 1;
    try {
      const row = parseCSVRow(lines[i]);
      const parsedItem = parseRowToMediaItem(row, headerMap, rowNumber);
      
      if (parsedItem.errors.length > 0) {
        errors.push(...parsedItem.errors.map(err => `Row ${rowNumber}: ${err}`));
      }
      
      if (parsedItem.warnings.length > 0) {
        warnings.push(...parsedItem.warnings.map(warn => `Row ${rowNumber}: ${warn}`));
      }

      if (parsedItem.item) {
        data.push(parsedItem.item);
      }
    } catch (error) {
      errors.push(`Row ${rowNumber}: Failed to parse - ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  return {
    data,
    errors,
    warnings,
    totalRows: lines.length - 1, // Exclude header
    validRows: data.length
  };
}

/**
 * Parse a single CSV row handling quotes and commas
 */
function parseCSVRow(row: string): string[] {
  const result: string[] = [];
  let current = '';
  let inQuotes = false;
  let i = 0;

  while (i < row.length) {
    const char = row[i];
    const nextChar = row[i + 1];

    if (char === '"') {
      if (inQuotes && nextChar === '"') {
        // Escaped quote
        current += '"';
        i += 2;
      } else {
        // Toggle quote state
        inQuotes = !inQuotes;
        i++;
      }
    } else if (char === ',' && !inQuotes) {
      // End of field
      result.push(current.trim());
      current = '';
      i++;
    } else {
      current += char;
      i++;
    }
  }

  // Add the last field
  result.push(current.trim());
  return result;
}

/**
 * Create a map of header names to column indices
 */
function createHeaderMap(headers: string[]): Map<string, number> {
  const map = new Map<string, number>();
  headers.forEach((header, index) => {
    map.set(header.toLowerCase().trim(), index);
  });
  return map;
}

/**
 * Parse a row into a MediaItem object
 */
function parseRowToMediaItem(
  row: string[], 
  headerMap: Map<string, number>, 
  rowNumber: number
): { item: Partial<MediaItem> | null; errors: string[]; warnings: string[] } {
  const errors: string[] = [];
  const warnings: string[] = [];
  const item: Partial<MediaItem> = {};

  // Helper function to get field value
  const getField = (fieldName: string): string => {
    const index = headerMap.get(fieldName.toLowerCase());
    return index !== undefined ? (row[index] || '').trim() : '';
  };

  // Helper function to parse multi-value fields (semicolon separated)
  const parseMultiValue = (value: string): string[] => {
    return value ? value.split(';').map(v => v.trim()).filter(v => v) : [];
  };

  // Helper function to parse boolean fields
  const parseBoolean = (value: string): boolean => {
    const lower = value.toLowerCase();
    return lower === 'true' || lower === 'yes' || lower === '1';
  };

  try {
    // Required fields
    const title = getField('Title');
    const type = getField('Type');
    const yearStr = getField('Year');

    if (!title) errors.push('Title is required');
    if (!type) errors.push('Type is required');
    if (!yearStr) errors.push('Year is required');

    // Validate type
    const normalizedType = type.toLowerCase();
    if (type && !['movie', 'series', 'web series', 'requested'].includes(normalizedType)) {
      errors.push(`Invalid type: ${type}. Must be 'movie', 'series', 'web series', or 'requested'`);
    }

    // Parse year
    const year = parseInt(yearStr);
    if (yearStr && (isNaN(year) || year < 1900 || year > new Date().getFullYear() + 5)) {
      errors.push(`Invalid year: ${yearStr}`);
    }

    // If there are critical errors, return early
    if (errors.length > 0) {
      return { item: null, errors, warnings };
    }

    // Basic fields
    item.id = `bulk-${Date.now()}-${rowNumber}`;
    item.title = title;
    item.description = getField('Description');
    item.type = normalizedType === 'web series' ? 'series' : normalizedType as any;
    item.category = getField('Category');
    item.year = year;

    // Section IDs - parse as semicolon-separated values and convert to numbers
    const sectionIdsStr = getField('Section IDs');
    item.section_ids = sectionIdsStr ?
      parseMultiValue(sectionIdsStr).map(id => parseInt(id.trim())).filter(id => !isNaN(id)) :
      [];

    // Multi-value fields
    item.genres = parseMultiValue(getField('Genres'));
    item.languages = parseMultiValue(getField('Languages'));
    item.quality = parseMultiValue(getField('Quality'));
    item.audioTracks = parseMultiValue(getField('Audio Tracks'));

    // Quality label fields
    item.qualityLabel = getField('Quality Label') || 'none';
    item.customQualityLabel = getField('Custom Quality Label') || '';

    // Boolean fields
    const status = getField('Status');
    item.isPublished = status ? parseBoolean(status) || status.toLowerCase() === 'published' : true;
    item.isFeatured = parseBoolean(getField('Featured'));
    item.addToCarousel = parseBoolean(getField('Carousel'));

    // Numeric/String fields
    item.imdbRating = getField('IMDb Rating');
    item.runtime = getField('Runtime');
    item.studio = getField('Studio');
    item.tags = getField('Tags');
    item.tmdbId = getField('TMDB ID');

    // URL fields
    item.image = getField('Poster URL');
    item.posterUrl = getField('Poster URL');
    item.thumbnailUrl = getField('Thumbnail URL');
    item.coverImage = getField('Cover Image');
    item.trailer = getField('Trailer URL');
    item.subtitleUrl = getField('Subtitle URL');

    // Video links (pipe separated for multiple links)
    const videoLinks = getField('Video Links');
    const secureVideoLinks = getField('Secure Video Links');
    
    if (videoLinks) {
      item.videoLinks = videoLinks;
    }
    if (secureVideoLinks) {
      item.secureVideoLinks = secureVideoLinks;
    }

    // Web series specific fields
    if (item.type === 'series') {
      const totalSeasons = getField('Total Seasons');
      const totalEpisodes = getField('Total Episodes');
      
      if (totalSeasons) {
        const seasons = parseInt(totalSeasons);
        if (!isNaN(seasons)) {
          item.totalSeasons = seasons;
        }
      }
      
      if (totalEpisodes) {
        const episodes = parseInt(totalEpisodes);
        if (!isNaN(episodes)) {
          item.totalEpisodes = episodes;
        }
      }
      
      // Initialize empty seasons array
      item.seasons = [];
    }

    // Timestamps
    const createdAt = getField('Created At');
    const updatedAt = getField('Updated At');
    
    item.createdAt = createdAt || new Date().toISOString();
    item.updatedAt = updatedAt || new Date().toISOString();

    // Validation warnings
    if (!item.description) warnings.push('Description is empty');
    if (!item.genres || item.genres.length === 0) warnings.push('No genres specified');
    if (!item.image && !item.posterUrl) warnings.push('No poster image URL provided');

    return { item, errors, warnings };
  } catch (error) {
    errors.push(`Parsing error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    return { item: null, errors, warnings };
  }
}

/**
 * Generate a sample CSV template
 */
export function generateSampleCSV(): string {
  const sampleData = [
    [
      'Movie Title Example',
      'A thrilling action movie with amazing special effects',
      'movie',
      'English Movies',
      '1;3',
      '2024',
      'Action;Sci-Fi;Thriller',
      'English;Spanish',
      'Published',
      'Yes',
      'No',
      '8.5',
      '120',
      'Example Studios',
      'action,blockbuster,2024',
      'https://example.com/poster.jpg',
      'https://example.com/thumb.jpg',
      'https://example.com/cover.jpg',
      'https://example.com/trailer.mp4',
      'https://example.com/subtitles.srt',
      'https://player.com/embed/123',
      'encrypted_link_here',
      'HD;BluRay',
      '4K',
      'IMAX Enhanced',
      'English;Spanish;French',
      'tt1234567',
      '',
      '',
      '2024-01-15T10:30:00Z',
      '2024-01-15T10:30:00Z'
    ],
    [
      'Web Series Example',
      'An exciting web series with multiple seasons',
      'series',
      'English Web Series',
      '2;4',
      '2023',
      'Drama;Mystery',
      'English',
      'Published',
      'Yes',
      'Yes',
      '9.0',
      '45',
      'Streaming Network',
      'drama,mystery,series',
      'https://example.com/series-poster.jpg',
      'https://example.com/series-thumb.jpg',
      'https://example.com/series-cover.jpg',
      'https://example.com/series-trailer.mp4',
      '',
      'https://player.com/embed/456|https://player.com/embed/789',
      'encrypted_link_1|encrypted_link_2',
      'HD;WEB',
      'HD',
      '',
      'English',
      'tt7654321',
      '3',
      '24',
      '2023-06-01T08:00:00Z',
      '2024-01-10T15:45:00Z'
    ]
  ];

  const csvContent = [CSV_HEADERS.join(','), ...sampleData.map(row => 
    row.map(cell => `"${cell.replace(/"/g, '""')}"`).join(',')
  )].join('\n');

  return csvContent;
}
