# StreamDB Online - Complete Project Documentation

## 🎯 Project Overview

StreamDB Online is a comprehensive streaming database website featuring movies and web series with an advanced admin panel for content management. The project uses a modern tech stack with React frontend, Node.js/Express backend, MySQL database, and a two-tier offshore VPS hosting setup with reverse proxy architecture.

## 🏗️ Architecture Overview

### System Architecture
```
User → Cloudflare → Reverse Proxy (*************) → Backend Server (***********)
                                                   ↓
                                    Nginx (80/443) + Apache (Internal)
                                                   ↓
                                    Node.js App (3001) + FastPanel2 (8888)
                                                   ↓
                                              MySQL Database (3306)
```

### Technology Stack
- **Frontend**: React 18 + TypeScript + Vite + Tailwind CSS
- **Backend**: Node.js v20.19.3 + Express.js + MySQL2
- **Database**: MySQL 8.0 with socket connections
- **Authentication**: JWT-based with session management
- **Process Management**: PM2 v6.0.8
- **Web Servers**: Dual setup - Apache 2.4 + Nginx (high performance)
- **Server Management**: FastPanel2 control panel
- **Operating System**: Ubuntu 24.04.2 LTS (Kernel 6.8.0-63-generic)
- **Hosting**: Two-tier offshore VPS setup

## 🌐 Infrastructure Setup

### Server Configuration

#### Reverse Proxy Server (*************)
- **Purpose**: SSL termination, load balancing, security filtering
- **Services**: Nginx reverse proxy, Cloudflare integration
- **Ports**: 80 (HTTP), 443 (HTTPS)
- **Configuration**: `/etc/nginx/sites-available/streamdb-proxy`

#### Backend Server (***********)
- **Operating System**: Ubuntu 24.04.2 LTS (Kernel 6.8.0-63-generic)
- **CPU**: QEMU Virtual CPU version 2.5+ @ 2.0GHz
- **Memory**: 4GB RAM (297.4M used by MySQL)
- **Storage**: 38GB total, 6.0GB used, 32GB available
- **Purpose**: Application hosting, database, file storage
- **Services**: Node.js app, MySQL, Apache, Nginx, FastPanel2
- **Ports**:
  - 3001 (Node.js - IPv6)
  - 3306 (MySQL - localhost only)
  - 80/443 (Nginx - public)
  - 8888 (FastPanel2 HTTPS)
- **Application Path**: `/var/www/streamdb_root/data/www/streamdb.online/`
- **Management**: FastPanel2 control panel for server administration

### Domain & SSL
- **Primary Domain**: streamdb.online
- **SSL**: Managed by Cloudflare
- **FastPanel Access**: fastpanel.streamdb.online:8888

## �️ Server Management & Web Server Architecture

### FastPanel2 Control Panel
- **Version**: FastPanel2 (Latest)
- **Access**: Port 8888 (HTTPS)
- **Services Managed**:
  - Apache 2.4 configuration
  - Nginx configuration
  - MySQL database management
  - SSL certificate management
  - User account management
- **Configuration Files**:
  - `/etc/nginx/fastpanel2-sites/streamdb_root/`
  - `/etc/apache2/fastpanel2-sites/streamdb_root/`
  - `/etc/mysql/my.cnf.fastpanel/99-fastpanel.cnf`

### Dual Web Server Architecture
```
Internet Traffic (Port 80/443)
         ↓
    Nginx (Frontend)
    - SSL termination
    - Static file serving
    - Load balancing
         ↓
    Apache (Backend)
    - PHP processing
    - FastPanel integration
    - Application routing
         ↓
    Node.js Application (Port 3001)
    - API endpoints
    - Dynamic content
    - Database connections
```

### Web Server Configuration
- **Nginx**: Primary web server handling public traffic
  - Listens on ports 80 (HTTP) and 443 (HTTPS)
  - Handles SSL termination and static content
  - Proxies API requests to Node.js application
- **Apache**: Secondary server for FastPanel integration
  - Manages FastPanel2 web interface
  - Handles server administration tasks
  - Integrated with FastPanel2 configuration system

## �🗄️ Database Architecture

### Database Configuration
- **Name**: `stream_db`
- **Connection**: Socket-based (`/var/run/mysqld/mysqld.sock`)
- **Charset**: utf8mb4_unicode_ci
- **Security**: Local-only access, no external exposure

### Complete Database Schema
```sql
# Content Management Tables
content                    # Movies and web series (9 records)
├── seasons               # Web series seasons (5 records)
└── episodes              # Individual episodes (9 records)

# Content Organization
categories                # Content categorization
content_sections          # Homepage sections
content_section_mappings  # Section-content relationships
section_categories        # Section category mappings
section_content_types     # Content type definitions

# Authentication & Security
admin_users              # Admin authentication
auth_tokens              # Authentication tokens
sessions                 # Session management
user_sessions            # User session tracking
admin_security_logs      # Security audit trail
security_logs            # General security logs
login_attempts           # Failed login tracking
password_reset_tokens    # Password reset functionality

# Analytics & Tracking
ad_blocker_tracking      # Ad blocker detection
```

### Key Relationships
- `content.id` → `seasons.content_id` (One-to-Many)
- `seasons.id` → `episodes.season_id` (One-to-Many)
- `content.category` → `categories.name` (Many-to-One)
- `content.section_id` → `sections.id` (Many-to-One)

## 🔧 Backend API Architecture

### Server Structure
```
server/
├── index.js                 # Main application entry
├── config/
│   └── database.js         # MySQL connection config
├── routes/
│   ├── auth.js            # Authentication endpoints
│   ├── content.js         # Content CRUD operations
│   ├── admin.js           # Admin panel operations
│   ├── categories.js      # Category management
│   ├── sections.js        # Homepage sections
│   └── episodes.js        # Web series episodes
├── middleware/
│   └── auth.js            # JWT authentication middleware
└── services/
    └── mysql-health.js    # Database health monitoring
```

### API Endpoints

#### Authentication (`/api/auth`)
- `POST /login` - Admin login with JWT
- `POST /logout` - Session termination
- `GET /verify` - Token validation
- `POST /change-password` - Password updates

#### Content Management (`/api/content`)
- `GET /` - Fetch all content with filtering
- `POST /` - Create new content
- `PUT /:id` - Update existing content
- `DELETE /:id` - Remove content
- `GET /:id` - Get specific content details

#### Episodes Management (`/api/episodes`)
- `GET /content/:contentId` - Get all seasons/episodes
- `POST /seasons` - Create new season
- `POST /episodes` - Create new episode
- `PUT /seasons/:id` - Update season
- `PUT /episodes/:id` - Update episode
- `DELETE /seasons/:id` - Remove season
- `DELETE /episodes/:id` - Remove episode

#### Admin Operations (`/api/admin`)
- `GET /stats` - Dashboard statistics
- `GET /security-logs` - Audit trail
- `POST /bulk-upload` - CSV data import

## ⚛️ Frontend Architecture

### Component Structure
```
src/
├── pages/
│   ├── Index.tsx              # Homepage with dynamic sections
│   ├── AdminPanel.tsx         # Admin dashboard
│   ├── ContentPage.tsx        # Individual content pages
│   ├── AllMovies.tsx          # Movies listing
│   └── AllSeries.tsx          # Web series listing
├── components/
│   ├── admin/
│   │   ├── AddTitleForm.tsx   # Content creation form
│   │   ├── ContentManager.tsx # Content management
│   │   ├── WebSeriesManager.tsx # Seasons/episodes management
│   │   └── SectionsManager.tsx # Homepage sections
│   ├── HeroCarousel.tsx       # Homepage carousel
│   ├── CardGrid.tsx           # Content grid display
│   ├── SecureVideoPlayer.tsx  # iFrame video player
│   └── Header.tsx             # Navigation header
├── contexts/
│   └── AuthContext.tsx        # Authentication state
└── services/
    └── apiService.ts          # API communication
```

### Key Features

#### Dynamic Homepage
- **Hero Carousel**: Maximum 10 featured items
- **Content Sections**: Database-driven sections (Movies, Web Series, etc.)
- **Responsive Design**: Mobile-first approach (320px-1024px)

#### Admin Panel
- **Secure Access**: JWT-based authentication
- **Content Management**: Full CRUD operations
- **Web Series Management**: Seasons and episodes
- **Section Management**: Homepage section configuration
- **TMDB/OMDb Integration**: Automatic metadata fetching

#### Video Player
- **Universal Embed Support**: All streaming platforms
- **Security**: Server-side embed link protection
- **Responsive**: Dynamic aspect ratio detection
- **Platform Detection**: Automatic player optimization

## 🔐 Security Implementation

### Authentication System
- **JWT Tokens**: Secure session management
- **Password Hashing**: bcrypt with 12 rounds
- **Session Storage**: Database-backed sessions
- **Role-Based Access**: Admin-only panel access

### Database Security
- **Socket Connections**: Local-only MySQL access
- **Prepared Statements**: SQL injection prevention
- **Input Validation**: Server-side data sanitization
- **Connection Pooling**: Resource management

### Network Security
- **Reverse Proxy**: Traffic filtering and SSL termination
- **Rate Limiting**: API endpoint protection
- **CORS Configuration**: Cross-origin request control
- **Security Headers**: XSS and clickjacking protection
- **UFW Firewall**: Active with specific port rules
  - SSH (22), HTTP (80), HTTPS (443), FastPanel (8888)
  - Special allowance for reverse proxy server (*************)
- **IPTables**: Default DROP policy with UFW management

## 🚀 Deployment Workflow

### Development to Production
1. **Local Development**: `G:\My Websites\Catalogue-Website\the-stream-db\Streaming_DB`
2. **Build Process**: `npm run build` (creates `dist/` folder)
3. **Backend Deployment**: Upload to `/var/www/streamdb_root/data/www/streamdb.online/`
4. **Process Management**: PM2 restart and monitoring via FastPanel2
5. **Frontend Deployment**: Static files served by Node.js through Apache/Nginx

### PM2 Process Management
```bash
# Main application (Production)
pm2 start index.js --name "streamdb-online"

# Process monitoring
pm2 status
pm2 logs streamdb-online
pm2 restart streamdb-online

# Current Production Status:
# ┌────┬─────────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┐
# │ id │ name            │ version │ mode    │ pid      │ uptime │ ↺    │ status    │
# ├────┼─────────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┤
# │ 0  │ streamdb-online │ 1.0.0   │ fork    │ 657889   │ 6h     │ 6752 │ online    │
# └────┴─────────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┘
```

### Environment Configuration
```bash
# Production .env (Located at: /var/www/streamdb_root/data/www/streamdb.online/server/.env)
NODE_ENV=production
PORT=3001
DB_HOST=localhost
DB_SOCKET=/var/run/mysqld/mysqld.sock
DB_NAME=stream_db
DB_USER=stream_db_admin
JWT_SECRET=secure_jwt_secret
FRONTEND_URL=https://streamdb.online

# Production Server Specifications
OS=Ubuntu 24.04.2 LTS
KERNEL=6.8.0-63-generic
NODE_VERSION=v20.19.3
NPM_VERSION=10.8.2
PM2_VERSION=6.0.8
```

## 📊 Content Management System

### Content Types
- **Movies**: Single video content with metadata
- **Web Series**: Multi-season content with episodes
- **Requested**: User-requested content tracking

### Metadata Fields
- Basic: Title, description, year, category
- Enhanced: TMDB ID, IMDB rating, runtime, studio
- Media: Poster, thumbnail, cover image, trailer
- Security: Encrypted video links, subtitle URLs

### Web Series Structure
```
Web Series
├── Season 1
│   ├── Episode 1 (Title + Video Link)
│   ├── Episode 2 (Title + Video Link)
│   └── ...
├── Season 2
│   └── ...
```

### Category System
18 predefined categories:
- Action, Adventure, Animation, Comedy, Crime
- Documentary, Drama, Family, Fantasy, History
- Horror, Music, Mystery, Romance, Science Fiction
- Thriller, War, Western

## 🔧 Maintenance & Monitoring

### Health Monitoring
- **Application Status**: PM2 process monitoring (streamdb-online)
- **Database Status**: MySQL service monitoring via systemctl
- **Web Server Status**: Nginx and Apache service monitoring
- **FastPanel2 Status**: Control panel service monitoring
- **Log Management**: Centralized logging system
  - Application logs: `/var/www/streamdb_root/data/www/streamdb.online/logs/`
  - System logs: `/var/log/` (apache2, nginx, mysql, fastpanel2)
- **Performance Metrics**: Resource usage tracking
  - Current memory usage: ~400MB total
  - MySQL: 297.4MB memory usage
  - Node.js: 85.6MB memory usage

### System Services Status
```bash
# Active Services on Production Server
apache2.service          # The Apache HTTP Server
fastpanel2-apps.service  # FASTPANEL applications pool
fastpanel2-nginx.service # FASTPANEL web server
fastpanel2.service       # FASTPANEL control panel
mysql.service            # MySQL Community Server
nginx.service            # nginx - high performance web server
```

### Backup Strategy
- **Database Backups**: Automated daily backups
- **File Backups**: Static assets and uploads
- **Configuration Backups**: Environment and config files
- **Rollback Capability**: Quick restoration procedures

### Update Procedures
1. **Code Updates**: Git-based deployment
2. **Database Migrations**: Schema update scripts
3. **Dependency Updates**: Package manager updates
4. **Security Patches**: Regular security updates

## 🎨 UI/UX Features

### Design System
- **Color Scheme**: Dark theme (#0a0a0a background, #e6cb8e primary)
- **Typography**: Optimized font weights for readability
- **Responsive Breakpoints**: 320px, 768px, 1024px, 1280px
- **Component Library**: Tailwind CSS + shadcn/ui

### User Experience
- **Search Functionality**: Global content search
- **Navigation**: Intuitive menu structure
- **Loading States**: Smooth transitions and feedback
- **Error Handling**: User-friendly error messages

## 📱 Mobile Optimization

### Responsive Design
- **Mobile-First**: Optimized for 320px+ screens
- **Touch-Friendly**: Large tap targets and gestures
- **Performance**: Optimized images and lazy loading
- **Navigation**: Collapsible mobile menu

### Video Player Mobile
- **Aspect Ratios**: Dynamic mobile-friendly ratios
- **Touch Controls**: Native mobile video controls
- **Fullscreen**: Seamless fullscreen experience
- **Performance**: Optimized for mobile bandwidth

## 🔄 Data Flow Architecture

### Frontend to Backend Communication
```
React Component → API Service → Express Route → Database Query → Response
     ↓              ↓              ↓              ↓              ↓
User Action → HTTP Request → Route Handler → MySQL Query → JSON Response
```

### Authentication Flow
```
1. User Login → JWT Token Generation → Session Storage
2. API Requests → Token Validation → Route Access
3. Token Expiry → Automatic Logout → Re-authentication
```

### Content Management Flow
```
Admin Panel → Form Submission → API Validation → Database Update → Cache Refresh → Frontend Update
```

## 🛠️ Development Environment Setup

### Local Development
```bash
# Clone repository
git clone <repository-url>
cd Streaming_DB

# Install dependencies
npm install

# Setup environment
cp server/.env.example server/.env
# Edit server/.env with local database credentials

# Start development servers
npm run dev          # Frontend (Vite)
cd server && npm run dev  # Backend (nodemon)
```

### Database Setup
```sql
-- Create database
CREATE DATABASE streamdb_database CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Import schema
SOURCE database/complete_schema.sql;

-- Import initial data
SOURCE database/initial_data.sql;
```

### Testing Environment
```bash
# Run tests
npm test

# Specific test files
npm test -- aspectRatioTest.ts
npm test -- embed-link-validation.test.ts
npm test -- iframe-config.test.ts
```

## 🔧 Configuration Management

### Environment Variables
```bash
# Database Configuration
DB_HOST=localhost
DB_USER=streamdb_user
DB_PASSWORD=secure_password
DB_NAME=streamdb_database
DB_SOCKET=/var/run/mysqld/mysqld.sock

# Security Configuration
JWT_SECRET=ultra_secure_jwt_secret_change_this
SESSION_SECRET=ultra_secure_session_secret_change_this
BCRYPT_ROUNDS=12

# API Configuration
TMDB_API_KEY=your_tmdb_api_key
OMDB_API_KEY=your_omdb_api_key

# Server Configuration
PORT=3001
NODE_ENV=production
FRONTEND_URL=https://streamdb.online
```

### Nginx Configuration
```nginx
# Reverse Proxy (*************)
upstream backend {
    server ***********:3001;
}

server {
    listen 443 ssl http2;
    server_name streamdb.online;

    location /api/ {
        proxy_pass http://backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    location / {
        proxy_pass http://backend;
    }
}
```

## 📋 Operational Procedures

### Daily Operations
1. **Health Checks**: Monitor PM2 processes and database connectivity
2. **Log Review**: Check error logs for issues
3. **Performance Monitoring**: Review response times and resource usage
4. **Content Updates**: Process new content additions through admin panel

### Weekly Maintenance
1. **Database Optimization**: Run OPTIMIZE TABLE commands
2. **Log Rotation**: Archive and compress old log files
3. **Security Updates**: Apply system and dependency updates
4. **Backup Verification**: Test backup restoration procedures

### Emergency Procedures
1. **Service Restart**: `pm2 restart streamdb-online`
2. **Web Server Restart**: `systemctl restart nginx apache2`
3. **FastPanel2 Restart**: `systemctl restart fastpanel2`
4. **Database Recovery**: Restore from latest backup
5. **Rollback Deployment**: Use previous version backup
6. **Traffic Rerouting**: Cloudflare maintenance mode

## 🔒 Firewall & Security Configuration

### UFW Firewall Rules
```bash
# Active UFW Configuration
Status: active

To                         Action      From
--                         ------      ----
22/tcp                     ALLOW       Anywhere
80/tcp                     ALLOW       Anywhere
443/tcp                    ALLOW       Anywhere
8888/tcp                   ALLOW       Anywhere
Anywhere                   ALLOW       *************  # Reverse Proxy Server
```

### IPTables Configuration
- **Default Policy**: DROP (secure by default)
- **Management**: Handled by UFW (Uncomplicated Firewall)
- **Custom Rules**: Special allowance for reverse proxy server
- **Port Access**:
  - SSH (22): Global access
  - HTTP/HTTPS (80/443): Global access
  - FastPanel (8888): Global access
  - MySQL (3306): Localhost only
  - Node.js (3001): IPv6 localhost only

## 🚨 Troubleshooting Guide

### Common Issues

#### Database Connection Errors
```bash
# Check MySQL status
systemctl status mysql

# Test database connection
node server/test-db-connection.js

# Check socket permissions
ls -la /var/run/mysqld/mysqld.sock
```

#### PM2 Process Issues
```bash
# Check process status
pm2 status

# View logs (Production process name)
pm2 logs streamdb-online

# Restart specific process
pm2 restart streamdb-online

# Full restart
pm2 restart all

# Check process details
pm2 show streamdb-online
```

#### Frontend Build Issues
```bash
# Clear cache and rebuild
rm -rf node_modules dist
npm install
npm run build

# Check build output
ls -la dist/
```

### Performance Optimization

#### Database Optimization
```sql
-- Index optimization
SHOW INDEX FROM content;
ANALYZE TABLE content, seasons, episodes;
OPTIMIZE TABLE content, seasons, episodes;

-- Query performance
EXPLAIN SELECT * FROM content WHERE category = 'Action';
```

#### Frontend Optimization
- **Image Optimization**: WebP format, lazy loading
- **Code Splitting**: Dynamic imports for large components
- **Caching**: Browser caching for static assets
- **Minification**: CSS and JavaScript compression

## 📊 Analytics & Monitoring

### Key Metrics
- **Response Times**: API endpoint performance
- **Database Queries**: Query execution times
- **User Engagement**: Content view statistics
- **Error Rates**: Application error frequency

### Monitoring Tools
- **PM2 Monitoring**: Process health and resource usage
- **MySQL Slow Query Log**: Database performance analysis
- **Nginx Access Logs**: Traffic patterns and errors
- **Custom Health Checks**: Application-specific monitoring

## 🔮 Future Enhancements

### Planned Features
1. **User Registration**: Public user accounts and profiles
2. **Content Rating**: User rating and review system
3. **Recommendation Engine**: AI-powered content suggestions
4. **Mobile App**: React Native mobile application
5. **CDN Integration**: Global content delivery network

### Technical Improvements
1. **Microservices**: Split monolith into smaller services
2. **Caching Layer**: Redis for improved performance
3. **Search Enhancement**: Elasticsearch integration
4. **Real-time Features**: WebSocket implementation
5. **API Versioning**: Backward-compatible API evolution

---

*This comprehensive documentation covers all aspects of the StreamDB Online project from architecture to operations. For specific implementation details, refer to the codebase and individual component documentation.*
