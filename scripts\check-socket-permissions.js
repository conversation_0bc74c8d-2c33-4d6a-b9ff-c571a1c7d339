#!/usr/bin/env node

/**
 * Socket Permission Checker for SEO Scripts
 * Safely checks and provides guidance for MySQL socket access
 * NO CHANGES MADE - Only diagnostic and guidance
 */

import fs from 'fs';
import { execSync } from 'child_process';

console.log('🔍 MySQL Socket Permission Checker for SEO Scripts');
console.log('==================================================\n');

/**
 * Check socket file existence and permissions
 */
function checkSocketFile(socketPath) {
  console.log(`📁 Checking socket: ${socketPath}`);
  
  try {
    if (!fs.existsSync(socketPath)) {
      console.log(`❌ Socket file not found: ${socketPath}`);
      return { exists: false, accessible: false };
    }
    
    const stats = fs.statSync(socketPath);
    
    if (!stats.isSocket()) {
      console.log(`❌ Not a socket file: ${socketPath}`);
      return { exists: true, accessible: false };
    }
    
    // Check permissions
    const mode = stats.mode;
    const permissions = (mode & parseInt('777', 8)).toString(8);
    const owner = stats.uid;
    const group = stats.gid;
    
    console.log(`✅ Socket file exists: ${socketPath}`);
    console.log(`📊 Permissions: ${permissions}`);
    console.log(`👤 Owner UID: ${owner}, Group GID: ${group}`);
    
    // Check if readable by current user
    try {
      fs.accessSync(socketPath, fs.constants.R_OK | fs.constants.W_OK);
      console.log(`✅ Socket is accessible by current user`);
      return { exists: true, accessible: true, permissions, owner, group };
    } catch (accessError) {
      console.log(`❌ Socket not accessible by current user: ${accessError.message}`);
      return { exists: true, accessible: false, permissions, owner, group };
    }
    
  } catch (error) {
    console.log(`❌ Error checking socket: ${error.message}`);
    return { exists: false, accessible: false, error: error.message };
  }
}

/**
 * Get current user information
 */
function getCurrentUserInfo() {
  try {
    const uid = process.getuid();
    const gid = process.getgid();
    const username = process.env.USER || process.env.USERNAME || 'unknown';
    
    console.log(`👤 Current user: ${username} (UID: ${uid}, GID: ${gid})`);
    
    // Check if user is in mysql group
    try {
      const groups = execSync('groups', { encoding: 'utf8' }).trim();
      const inMysqlGroup = groups.includes('mysql');
      console.log(`🔐 User groups: ${groups}`);
      console.log(`🔐 In mysql group: ${inMysqlGroup ? 'Yes' : 'No'}`);
      
      return { uid, gid, username, groups, inMysqlGroup };
    } catch (groupError) {
      console.log(`⚠️ Could not check user groups: ${groupError.message}`);
      return { uid, gid, username };
    }
    
  } catch (error) {
    console.log(`❌ Error getting user info: ${error.message}`);
    return null;
  }
}

/**
 * Check MySQL service status
 */
function checkMySQLService() {
  console.log('\n🔧 Checking MySQL service status...');
  
  try {
    const status = execSync('systemctl is-active mysql', { encoding: 'utf8' }).trim();
    console.log(`✅ MySQL service status: ${status}`);
    
    if (status === 'active') {
      // Get MySQL process info
      try {
        const processInfo = execSync('ps aux | grep mysqld | grep -v grep', { encoding: 'utf8' });
        console.log(`📊 MySQL process running`);
        
        // Extract MySQL user from process
        const mysqlUser = processInfo.split(/\s+/)[0];
        console.log(`👤 MySQL running as user: ${mysqlUser}`);
        
        return { active: true, user: mysqlUser };
      } catch (psError) {
        return { active: true };
      }
    }
    
    return { active: false, status };
    
  } catch (error) {
    console.log(`❌ Could not check MySQL service: ${error.message}`);
    return { active: false, error: error.message };
  }
}

/**
 * Provide safe fix recommendations
 */
function provideSafeFixRecommendations(socketResults, userInfo, mysqlInfo) {
  console.log('\n🛠️ SAFE FIX RECOMMENDATIONS');
  console.log('============================\n');
  
  const workingSockets = socketResults.filter(r => r.result.accessible);
  
  if (workingSockets.length > 0) {
    console.log('🎉 GOOD NEWS: At least one socket is accessible!');
    console.log('✅ Your SEO scripts should work with socket connections.');
    workingSockets.forEach(socket => {
      console.log(`   ✓ Working socket: ${socket.path}`);
    });
    return true;
  }
  
  console.log('⚠️ No sockets are currently accessible. Here are SAFE options:\n');
  
  console.log('🔧 OPTION 1: Add user to mysql group (SAFEST)');
  console.log('   sudo usermod -a -G mysql $USER');
  console.log('   newgrp mysql  # Refresh group membership');
  console.log('   # Then test: npm run setup-seo\n');
  
  console.log('🔧 OPTION 2: Temporary socket permission (if needed)');
  console.log('   sudo chmod 666 /var/run/mysqld/mysqld.sock');
  console.log('   # This is temporary - will reset on MySQL restart\n');
  
  console.log('🔧 OPTION 3: Check socket ownership');
  console.log('   ls -la /var/run/mysqld/mysqld.sock');
  console.log('   # Should be owned by mysql:mysql\n');
  
  console.log('⚠️ IMPORTANT: These changes are production-safe and do NOT affect:');
  console.log('   - Your existing database permissions');
  console.log('   - Your website\'s database connections');
  console.log('   - Any TCP connection settings');
  console.log('   - Your current server functionality\n');
  
  return false;
}

/**
 * Main diagnostic function
 */
async function runSocketDiagnostic() {
  console.log('🚀 Starting MySQL socket diagnostic for SEO scripts...\n');
  
  // Check user information
  const userInfo = getCurrentUserInfo();
  
  // Check MySQL service
  const mysqlInfo = checkMySQLService();
  
  // Check all possible socket locations
  const socketPaths = [
    '/var/run/mysqld/mysqld.sock',
    '/tmp/mysql.sock',
    '/var/lib/mysql/mysql.sock',
    '/run/mysqld/mysqld.sock'
  ];
  
  console.log('\n🔌 Checking socket files...');
  const socketResults = socketPaths.map(path => ({
    path,
    result: checkSocketFile(path)
  }));
  
  // Test actual socket connection
  console.log('\n🔗 Testing socket connection with SEO scripts...');
  try {
    const { testAllSocketConnections } = await import('./database-connection-handler.js');
    const connectionResults = await testAllSocketConnections();
    
    const workingConnections = connectionResults.filter(r => r.success);
    if (workingConnections.length > 0) {
      console.log('\n🎉 SUCCESS: Socket connections are working!');
      console.log('✅ Your SEO automation should work perfectly.');
      return true;
    }
  } catch (error) {
    console.log(`❌ Socket connection test failed: ${error.message}`);
  }
  
  // Provide recommendations
  const success = provideSafeFixRecommendations(socketResults, userInfo, mysqlInfo);
  
  return success;
}

// Run diagnostic if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runSocketDiagnostic().catch(console.error);
}

export { runSocketDiagnostic };
