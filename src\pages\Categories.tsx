import { useState } from "react";
import { <PERSON> } from "react-router-dom";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import { But<PERSON> } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";
import { scrollToTop } from "@/utils/scrollToTop";

// Categories list matching the admin panel
const CATEGORIES = [
  "Hindi Movies",
  "Hindi Web Series", 
  "English Movies",
  "English Web Series",
  "Telugu Movies",
  "Telugu Web Series",
  "Tamil Movies",
  "Tamil Web Series",
  "Malayalam Movies",
  "Malayalam Web Series",
  "Korean Movies",
  "Korean Web Series",
  "Japanese Movies",
  "Japanese Web Series",
  "Anime",
  "Hindi Dubbed",
  "English Dubbed",
  "Animation",
  "New Releases",
  "Requested",
  "Drama"
];

export default function Categories() {
  return (
    <div className="flex flex-col min-h-screen bg-background">
      <Header />
      
      <main className="flex-1 max-w-7xl w-full mx-auto px-4 py-8">
        <div className="flex items-center justify-between mb-8">
          <h1
            className="stdb-heading"
            style={{
              color: "#e6cb8e",
              fontFamily: "'Koulen', Impact, Arial, sans-serif",
              fontWeight: 400,
              // Reduced font size by 25% for mobile and made it responsive to fit in one line
              fontSize: "clamp(1.125rem, 4vw, 2.25rem)", // Mobile: 1.125rem (25% smaller), Desktop: 2.25rem
              letterSpacing: "0.07em",
              margin: 0,
              textTransform: "uppercase",
              textShadow: "0 2px 16px #19191740, 0 1px 2px #0002",
              // Ensure single line display
              whiteSpace: "nowrap",
              overflow: "hidden",
              textOverflow: "ellipsis",
              maxWidth: "100%"
            }}
          >
            Browse by Categories
          </h1>
          <Link to="/" onClick={scrollToTop}>
            <Button variant="outline" className="flex items-center gap-2">
              <ArrowLeft className="w-4 h-4" />
              Back to Home
            </Button>
          </Link>
        </div>

        {/* Categories Grid */}
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3 sm:gap-4">
          {CATEGORIES.map((category) => (
            <Link
              key={category}
              to={`/category/${encodeURIComponent(category.toLowerCase().replace(/\s+/g, '-'))}`}
              onClick={scrollToTop}
              className="group relative overflow-hidden rounded-lg border border-border bg-card hover:bg-card/80 transition-all duration-300 hover:scale-105 hover:shadow-lg min-h-[100px] flex items-center"
            >
              <div className="p-4 sm:p-6 text-center w-full">
                <h3 className="text-base sm:text-lg font-semibold text-foreground group-hover:text-primary transition-colors">
                  {category}
                </h3>
                <p className="text-xs sm:text-sm text-muted-foreground mt-1 sm:mt-2">
                  Browse {category.toLowerCase()}
                </p>
              </div>
              
              {/* Hover effect overlay */}
              <div className="absolute inset-0 bg-primary/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
            </Link>
          ))}
        </div>

        {/* Additional Info */}
        <div className="mt-12 text-center">
          <p className="text-muted-foreground text-sm">
            Select a category to browse content filtered by language and type
          </p>
        </div>
      </main>

      <Footer />
    </div>
  );
}
