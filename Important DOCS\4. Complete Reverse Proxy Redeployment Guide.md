# Complete Reverse Proxy Setup Redeployment Guide
**For Non-Technical Users - Step by Step Instructions**

## Overview of Your Current Setup

Your current reverse proxy flow works like this:
```
Client Browser
     ↓
Cloudflare (Free Plan) - Domain: streamdb.online
     ↓
1st Offshore Reverse Proxy VPS: ************* (NGINX Reverse Proxy)
     ↓
2nd Offshore Backend VPS: *********** (FastPanel + MySQL + Website)
```

**Important:** This guide will help you recreate this exact setup on new servers while keeping your backend server IP (***********) completely hidden from the public.

---

## PHASE 1: PREPARATION (Do This First!)

### Step 1: Gather Your Current Server Information
**What to do:** Document your current setup details
**When to do:** Before ordering new servers
**Where to go:** Your current servers and Cloudflare dashboard

**Current Server Details (From Your Documentation):**

**Reverse Proxy Server (*************):**
- OS: Ubuntu 24.04.2 LTS
- CPU: 1 vCPU
- RAM: 1.4GB
- Disk: 8.7GB
- Hostname: backend2ndrevproxy

**Backend Server (***********):**
- OS: Ubuntu 24.04.2 LTS  
- CPU: 2 vCPU
- RAM: 3.8GB
- Disk: 38GB
- Hostname: backend1maindb
- Services: FastPanel, MySQL, Apache, NGINX, Node.js (port 3001)

### Step 2: Order New Servers
**What to do:** Order replacement servers with same or better specifications
**When to do:** After documenting current setup
**Where to go:** Your hosting provider

**Minimum Requirements for New Servers:**

**New Reverse Proxy Server:**
- OS: Ubuntu 24.04 LTS (latest)
- CPU: 1 vCPU (minimum)
- RAM: 2GB (recommended upgrade)
- Disk: 10GB SSD (minimum)
- Network: Unlimited bandwidth
- Location: Offshore (same region as current if possible)

**New Backend Server:**
- OS: Ubuntu 24.04 LTS (latest)
- CPU: 2 vCPU (minimum)
- RAM: 4GB (recommended upgrade)
- Disk: 40GB SSD (minimum)
- Network: Unlimited bandwidth
- Location: Offshore (same region as current if possible)

**What to expect:** Server provisioning takes 5-30 minutes
**If issues found:** Contact hosting provider support immediately

---

## PHASE 2: BACKEND SERVER SETUP (Do This Second!)

### Step 3: Setup New Backend Server
**What to do:** Install and configure all backend services
**When to do:** After new servers are provisioned
**Where to go:** SSH into your new backend server

#### Step 3.1: Initial Server Setup
```bash
# Connect to your new backend server
ssh root@[NEW_BACKEND_IP]

# Update system
apt update && apt upgrade -y

# Set hostname
hostnamectl set-hostname backend1maindb

# Configure timezone
timedatectl set-timezone UTC
```

**What to expect:** Commands will take 5-10 minutes to complete
**If issues found:** Check internet connectivity: `ping google.com`

#### Step 3.2: Install FastPanel Control Panel
```bash
# Download and install FastPanel
curl -sSL https://fastpanel.direct/install.sh | bash

# Follow the installation prompts
# Set admin password when prompted
# Note down the FastPanel URL and login details
```

**What to expect:** Installation takes 10-15 minutes
**If issues found:** 
- Check if port 8888 is accessible: `netstat -tulpn | grep 8888`
- Restart FastPanel: `systemctl restart fastpanel2`

#### Step 3.3: Configure MySQL Database
```bash
# Secure MySQL installation
mysql_secure_installation

# Create database for your website
mysql -u root -p
CREATE DATABASE streamdb_production;
CREATE USER 'streamdb_user'@'localhost' IDENTIFIED BY 'your_secure_password';
GRANT ALL PRIVILEGES ON streamdb_production.* TO 'streamdb_user'@'localhost';
FLUSH PRIVILEGES;
EXIT;
```

**What to expect:** Database setup takes 2-3 minutes
**If issues found:** Check MySQL status: `systemctl status mysql`

#### Step 3.4: Configure Firewall
```bash
# Install and configure UFW firewall
ufw --force enable
ufw default deny incoming
ufw default allow outgoing

# Allow essential ports
ufw allow 22/tcp    # SSH
ufw allow 80/tcp    # HTTP
ufw allow 443/tcp   # HTTPS
ufw allow 8888/tcp  # FastPanel
ufw allow 3001/tcp  # Node.js application

# Allow access from reverse proxy server only
ufw allow from [NEW_REVERSE_PROXY_IP]

# Check firewall status
ufw status verbose
```

**What to expect:** Firewall configuration takes 1-2 minutes
**If issues found:** Check UFW status: `ufw status`

### Step 4: Restore Website Data
**What to do:** Transfer your website files and database
**When to do:** After backend server is configured
**Where to go:** Both old and new backend servers

#### Step 4.1: Backup Current Website
```bash
# On OLD backend server (***********)
# Create backup directory
mkdir -p /tmp/website-backup

# Backup website files
tar -czf /tmp/website-backup/website-files.tar.gz /var/www/streamdb_root/

# Backup database
mysqldump -u root -p streamdb_production > /tmp/website-backup/database.sql

# Create archive of all backups
cd /tmp
tar -czf website-complete-backup.tar.gz website-backup/
```

#### Step 4.2: Transfer to New Server
```bash
# Transfer backup to new server
scp /tmp/website-complete-backup.tar.gz root@[NEW_BACKEND_IP]:/tmp/

# On NEW backend server
cd /tmp
tar -xzf website-complete-backup.tar.gz

# Restore website files
mkdir -p /var/www/streamdb_root/
tar -xzf website-backup/website-files.tar.gz -C /

# Restore database
mysql -u root -p streamdb_production < website-backup/database.sql

# Set proper permissions
chown -R www-data:www-data /var/www/streamdb_root/
chmod -R 755 /var/www/streamdb_root/
```

**What to expect:** Transfer takes 10-30 minutes depending on data size
**If issues found:** 
- Check disk space: `df -h`
- Verify file permissions: `ls -la /var/www/streamdb_root/`

---

## PHASE 3: REVERSE PROXY SERVER SETUP

### Step 5: Setup New Reverse Proxy Server
**What to do:** Install and configure NGINX reverse proxy
**When to do:** After backend server is ready
**Where to go:** SSH into your new reverse proxy server

#### Step 5.1: Initial Server Setup
```bash
# Connect to new reverse proxy server
ssh root@[NEW_REVERSE_PROXY_IP]

# Update system
apt update && apt upgrade -y

# Set hostname
hostnamectl set-hostname backend2ndrevproxy

# Install required packages
apt install -y nginx ufw fail2ban curl wget
```

#### Step 5.2: Configure NGINX
```bash
# Remove default NGINX configuration
rm /etc/nginx/sites-enabled/default

# Create streamdb.online configuration
nano /etc/nginx/sites-available/streamdb.online
```

**Copy this exact configuration (replace [NEW_BACKEND_IP] with your new backend server IP):**

```nginx
# StreamDB.online Reverse Proxy Configuration
# WebSocket upgrade map
map $http_upgrade $connection_upgrade {
    default upgrade;
    '' close;
}

# Rate limiting zones
limit_req_zone $binary_remote_addr zone=general:10m rate=10r/s;
limit_req_zone $binary_remote_addr zone=api:10m rate=5r/s;
limit_req_zone $binary_remote_addr zone=admin:10m rate=20r/s;

# Main website - HTTP to HTTPS redirect
server {
    listen 80;
    server_name streamdb.online www.streamdb.online;

    # Security headers even for redirects
    add_header X-Frame-Options "DENY" always;
    add_header X-Content-Type-Options "nosniff" always;

    # Redirect all HTTP to HTTPS
    return 301 https://$server_name$request_uri;
}

# Main website - HTTPS
server {
    listen 443 ssl http2;
    server_name streamdb.online www.streamdb.online;

    # SSL Configuration (Cloudflare handles certificates)
    ssl_certificate /etc/ssl/certs/cloudflare-origin.pem;
    ssl_certificate_key /etc/ssl/private/cloudflare-origin.key;

    # SSL Security Settings
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;

    # Security Headers
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains; preload" always;
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://pertawee.net https://al5sm.com; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self' https: https://pertawee.net https://al5sm.com; media-src 'self' https:; object-src 'none'; frame-src 'self' https:;" always;

    # Hide server information
    server_tokens off;

    # Rate limiting
    limit_req zone=general burst=20 nodelay;

    # Proxy settings for backend
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
    proxy_set_header X-Forwarded-Host $host;
    proxy_set_header X-Forwarded-Port $server_port;

    # Proxy timeouts
    proxy_connect_timeout 30s;
    proxy_send_timeout 30s;
    proxy_read_timeout 30s;
    proxy_buffering on;
    proxy_buffer_size 4k;
    proxy_buffers 8 4k;

    # API routes with rate limiting - Direct to Node.js
    location /api/ {
        limit_req zone=api burst=10 nodelay;
        proxy_pass http://[NEW_BACKEND_IP]:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_cache_bypass $http_upgrade;
    }

    # Admin panel routes with extra security - Direct to Node.js
    location /admin {
        limit_req zone=admin burst=5 nodelay;
        add_header X-Admin-Access "Restricted" always;
        proxy_pass http://[NEW_BACKEND_IP]:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_cache_bypass $http_upgrade;
    }

    # Static files and main application - Direct to Node.js
    location / {
        proxy_pass http://[NEW_BACKEND_IP]:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_cache_bypass $http_upgrade;

        # Cache static assets
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
            proxy_pass http://[NEW_BACKEND_IP]:3001;
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }

    # Health check endpoint
    location /health {
        proxy_pass http://[NEW_BACKEND_IP]:3001/api/health;
        access_log off;
    }

    # Block access to sensitive files
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }

    location ~ /(\.env|\.git|node_modules|server|database) {
        deny all;
        access_log off;
        log_not_found off;
    }

    # Logging
    access_log /var/log/nginx/streamdb_access.log;
    error_log /var/log/nginx/streamdb_error.log;
}

# FastPanel subdomain - HTTP to HTTPS redirect
server {
    listen 80;
    server_name fastpanel.streamdb.online;
    return 301 https://$server_name$request_uri;
}

# FastPanel subdomain - HTTPS
server {
    listen 443 ssl http2;
    server_name fastpanel.streamdb.online;

    # SSL Configuration (same as main site)
    ssl_certificate /etc/ssl/certs/cloudflare-origin.pem;
    ssl_certificate_key /etc/ssl/private/cloudflare-origin.key;

    # SSL Security Settings
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;

    # Security Headers for FastPanel (relaxed for functionality)
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "same-origin" always;

    # Proxy to FastPanel on backend server
    location / {
        limit_req zone=admin burst=50 nodelay;
        proxy_pass http://[NEW_BACKEND_IP]:8888;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection $connection_upgrade;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $host;
        proxy_set_header X-Forwarded-Port $server_port;
        proxy_set_header X-Scheme $scheme;
        proxy_cache_bypass $http_upgrade;
        proxy_buffering off;
        proxy_request_buffering off;
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }

    # Logging
    access_log /var/log/nginx/fastpanel_access.log;
    error_log /var/log/nginx/fastpanel_error.log;
}
```

#### Step 5.3: Enable NGINX Configuration
```bash
# Enable the site
ln -s /etc/nginx/sites-available/streamdb.online /etc/nginx/sites-enabled/

# Test NGINX configuration
nginx -t

# If test passes, restart NGINX
systemctl restart nginx
systemctl enable nginx
```

**What to expect:** NGINX should start without errors
**If issues found:**
- Check configuration syntax: `nginx -t`
- Check NGINX status: `systemctl status nginx`
- View error logs: `tail -f /var/log/nginx/error.log`

#### Step 5.4: Install SSL Certificates
**What to do:** Install Cloudflare Origin certificates
**When to do:** After NGINX is configured
**Where to go:** Cloudflare dashboard and reverse proxy server

```bash
# Create SSL directories
mkdir -p /etc/ssl/certs /etc/ssl/private

# Create Cloudflare Origin Certificate
# Go to Cloudflare Dashboard > SSL/TLS > Origin Server > Create Certificate
# Copy the certificate content and paste it here:
nano /etc/ssl/certs/cloudflare-origin.pem

# Copy the private key content and paste it here:
nano /etc/ssl/private/cloudflare-origin.key

# Set proper permissions
chmod 644 /etc/ssl/certs/cloudflare-origin.pem
chmod 600 /etc/ssl/private/cloudflare-origin.key
chown root:root /etc/ssl/certs/cloudflare-origin.pem
chown root:root /etc/ssl/private/cloudflare-origin.key

# Test NGINX configuration again
nginx -t

# Restart NGINX
systemctl restart nginx
```

**What to expect:** SSL certificates should be installed successfully
**If issues found:**
- Verify certificate format (should start with -----BEGIN CERTIFICATE-----)
- Check file permissions: `ls -la /etc/ssl/certs/cloudflare-origin.pem`

#### Step 5.5: Configure Firewall
```bash
# Configure UFW firewall
ufw --force enable
ufw default deny incoming
ufw default allow outgoing

# Allow essential ports
ufw allow 22/tcp   # SSH
ufw allow 80/tcp   # HTTP
ufw allow 443/tcp  # HTTPS

# Check firewall status
ufw status verbose
```

**What to expect:** Firewall should be active with proper rules
**If issues found:** Check UFW status: `ufw status`

---

## PHASE 4: CLOUDFLARE DNS MIGRATION

### Step 6: Prepare for DNS Migration
**What to do:** Lower DNS TTL values for faster switching
**When to do:** At least 1 hour before migration
**Where to go:** Cloudflare Dashboard

#### Step 6.1: Lower DNS TTL
1. **Login to Cloudflare Dashboard:** https://dash.cloudflare.com
2. **Select your domain:** streamdb.online
3. **Go to DNS section**
4. **For each A record (@, www, fastpanel):**
   - Click "Edit"
   - Change TTL from "Auto" to "5 minutes (300 seconds)"
   - Click "Save"

**What to expect:** Changes take effect immediately
**If issues found:** Wait 5 minutes and verify TTL changes

#### Step 6.2: Document Current DNS Settings
**Current DNS Records (From Your Documentation):**

| Type | Name | Content | Proxy Status | TTL |
|------|------|---------|--------------|-----|
| A | @ | ************* | Proxied | 300 |
| A | www | ************* | Proxied | 300 |
| A | fastpanel | ************* | Proxied | 300 |
| MX | streamdb.online | eforward3.registrar-servers.com | - | Auto |
| MX | streamdb.online | eforward2.registrar-servers.com | - | Auto |
| MX | streamdb.online | eforward1.registrar-servers.com | - | Auto |
| MX | streamdb.online | eforward4.registrar-servers.com | - | Auto |
| MX | streamdb.online | eforward5.registrar-servers.com | - | Auto |
| NS | streamdb.online | dns2.registrar-servers.com | - | Auto |
| NS | streamdb.online | dns1.registrar-servers.com | - | Auto |
| TXT | streamdb.online | "v=spf1 include:spf.efwd.registrar-servers.com ~all" | - | Auto |

### Step 7: Execute DNS Migration
**What to do:** Update A records to point to new reverse proxy server
**When to do:** After new servers are fully configured and tested
**Where to go:** Cloudflare Dashboard

#### Step 7.1: Update DNS Records
1. **Go to Cloudflare Dashboard > DNS**
2. **Update A record for "@" (root domain):**
   - Click "Edit" on the @ record
   - Change IP from `*************` to `[NEW_REVERSE_PROXY_IP]`
   - Keep "Proxied" status
   - Keep TTL at 300 seconds
   - Click "Save"

3. **Update A record for "www":**
   - Click "Edit" on the www record
   - Change IP from `*************` to `[NEW_REVERSE_PROXY_IP]`
   - Keep "Proxied" status
   - Keep TTL at 300 seconds
   - Click "Save"

4. **Update A record for "fastpanel":**
   - Click "Edit" on the fastpanel record
   - Change IP from `*************` to `[NEW_REVERSE_PROXY_IP]`
   - Keep "Proxied" status
   - Keep TTL at 300 seconds
   - Click "Save"

**What to expect:** DNS changes propagate within 5 minutes
**If issues found:**
- Check DNS propagation: `nslookup streamdb.online`
- Wait 5-10 minutes for full propagation

#### Step 7.2: Monitor Traffic Switch
```bash
# On new reverse proxy server, monitor access logs
tail -f /var/log/nginx/streamdb_access.log

# Check if traffic is coming to new server
# You should see incoming requests within 5-10 minutes
```

**What to expect:** Traffic should start flowing to new server within 5-10 minutes
**If issues found:**
- Check DNS resolution: `dig streamdb.online`
- Verify NGINX is running: `systemctl status nginx`

---

## PHASE 5: TESTING AND VERIFICATION

### Step 8: Test All Functionality
**What to do:** Verify all services work correctly
**When to do:** After DNS migration is complete
**Where to go:** Web browser and command line

#### Step 8.1: Test Main Website
1. **Open browser and visit:** https://streamdb.online
2. **Check that:**
   - Website loads correctly
   - All pages work
   - No SSL errors
   - All features function properly

**What to expect:** Website should work exactly as before
**If issues found:**
- Check NGINX error logs: `tail -f /var/log/nginx/streamdb_error.log`
- Verify backend server is accessible from reverse proxy

#### Step 8.2: Test FastPanel Access
1. **Open browser and visit:** https://fastpanel.streamdb.online
2. **Check that:**
   - FastPanel login page loads
   - You can login successfully
   - All FastPanel features work

**What to expect:** FastPanel should be fully functional
**If issues found:**
- Check FastPanel service: `systemctl status fastpanel2`
- Verify port 8888 is accessible from reverse proxy

#### Step 8.3: Test API Endpoints
```bash
# Test API health endpoint
curl -I https://streamdb.online/health

# Test API functionality
curl https://streamdb.online/api/[your-api-endpoint]
```

**What to expect:** APIs should respond correctly
**If issues found:**
- Check Node.js application status on backend server
- Verify port 3001 is accessible

### Step 9: Finalize Migration
**What to do:** Complete the migration process
**When to do:** After all testing is successful
**Where to go:** Cloudflare Dashboard

#### Step 9.1: Increase DNS TTL
1. **Go to Cloudflare Dashboard > DNS**
2. **For each A record (@, www, fastpanel):**
   - Click "Edit"
   - Change TTL from "5 minutes" to "1 hour (3600 seconds)"
   - Click "Save"

**What to expect:** TTL changes take effect immediately
**If issues found:** No issues expected for this step

#### Step 9.2: Update Documentation
**What to do:** Update your server documentation with new IP addresses

**New Server Information:**
- **New Reverse Proxy IP:** [NEW_REVERSE_PROXY_IP]
- **New Backend IP:** [NEW_BACKEND_IP] (if changed)
- **Migration Date:** [TODAY'S DATE]
- **Migration Status:** Complete

---

## PHASE 6: CLEANUP AND SECURITY

### Step 10: Secure New Servers
**What to do:** Implement additional security measures
**When to do:** After migration is complete
**Where to go:** Both new servers

#### Step 10.1: Install Fail2Ban
```bash
# On both servers
apt install -y fail2ban

# Configure Fail2Ban for SSH protection
cp /etc/fail2ban/jail.conf /etc/fail2ban/jail.local

# Edit configuration
nano /etc/fail2ban/jail.local

# Find [sshd] section and ensure it's enabled:
[sshd]
enabled = true
port = ssh
logpath = /var/log/auth.log
maxretry = 3
bantime = 3600

# Start and enable Fail2Ban
systemctl start fail2ban
systemctl enable fail2ban
```

#### Step 10.2: Change Default SSH Port (Optional but Recommended)
```bash
# Edit SSH configuration
nano /etc/ssh/sshd_config

# Change port from 22 to something else (e.g., 2222)
Port 2222

# Restart SSH service
systemctl restart sshd

# Update firewall rules
ufw delete allow 22/tcp
ufw allow 2222/tcp
```

**Important:** Test SSH connection on new port before closing current session!

### Step 11: Cleanup Old Servers
**What to do:** Safely decommission old servers
**When to do:** After 24-48 hours of successful operation
**Where to go:** Old servers and hosting provider

#### Step 11.1: Create Final Backup
```bash
# On old servers, create final backup
tar -czf /tmp/final-backup-$(date +%Y%m%d).tar.gz /etc /var/www /var/log

# Download backup to local machine
scp root@*************:/tmp/final-backup-*.tar.gz ./
scp root@***********:/tmp/final-backup-*.tar.gz ./
```

#### Step 11.2: Monitor New Servers
**What to do:** Monitor new servers for 24-48 hours
**What to watch for:**
- Server performance and resource usage
- Website availability and speed
- Error logs for any issues
- User feedback

**If everything works perfectly for 48 hours, you can safely cancel old servers.**

---

## TROUBLESHOOTING GUIDE

### Common Issues and Solutions

#### Issue: Website Not Loading
**Symptoms:** Browser shows "Site can't be reached" or timeout errors
**Solutions:**
1. Check DNS propagation: `nslookup streamdb.online`
2. Verify NGINX is running: `systemctl status nginx`
3. Check firewall rules: `ufw status`
4. Test direct IP access: `curl http://[NEW_REVERSE_PROXY_IP]`

#### Issue: SSL Certificate Errors
**Symptoms:** Browser shows "Not secure" or SSL warnings
**Solutions:**
1. Verify Cloudflare SSL mode is "Full (strict)"
2. Check origin certificate installation
3. Restart NGINX: `systemctl restart nginx`
4. Check certificate files exist and have correct permissions

#### Issue: FastPanel Not Accessible
**Symptoms:** FastPanel subdomain not loading
**Solutions:**
1. Check FastPanel service: `systemctl status fastpanel2`
2. Verify port 8888 is open: `netstat -tulpn | grep 8888`
3. Check NGINX configuration for fastpanel subdomain
4. Test direct access: `curl http://[NEW_BACKEND_IP]:8888`

#### Issue: Database Connection Errors
**Symptoms:** Website shows database connection errors
**Solutions:**
1. Check MySQL service: `systemctl status mysql`
2. Verify database credentials in application configuration
3. Test database connection: `mysql -u streamdb_user -p streamdb_production`
4. Check MySQL error logs: `tail -f /var/log/mysql/error.log`

---

## IMPORTANT SECURITY NOTES

### Critical Security Requirements

1. **Never expose backend server IP (***********) publicly**
   - All traffic must go through reverse proxy
   - Backend server should only accept connections from reverse proxy IP
   - Use firewall rules to enforce this restriction

2. **Keep all software updated**
   - Run `apt update && apt upgrade` weekly
   - Monitor security advisories for NGINX, MySQL, FastPanel

3. **Monitor access logs regularly**
   - Check for suspicious activity
   - Set up log rotation to prevent disk space issues
   - Consider implementing log monitoring tools

4. **Backup regularly**
   - Daily database backups
   - Weekly full server backups
   - Test backup restoration procedures

5. **Use strong passwords**
   - Change all default passwords
   - Use complex passwords for all accounts
   - Consider implementing SSH key authentication

---

## SUPPORT CONTACTS

If you encounter issues during migration:

1. **Hosting Provider Support:** Contact your VPS provider for server-related issues
2. **Cloudflare Support:** Use Cloudflare dashboard for DNS/SSL issues
3. **FastPanel Support:** Check FastPanel documentation for control panel issues

**Remember:** Always test changes in a staging environment first when possible, and keep backups of all configurations before making changes.

---

**Migration Complete!**

Your reverse proxy setup has been successfully redeployed to new servers while maintaining the same security and functionality. Your backend server IP remains hidden, and all traffic flows through the secure reverse proxy configuration.
