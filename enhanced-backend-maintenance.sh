#!/bin/bash

# ============================================================================
# StreamDB Enhanced Backend Server Automated Maintenance Script
# Server: backend1maindb (***********)
# Purpose: Comprehensive weekly maintenance with advanced monitoring
# Schedule: Every Thursday at 00:00 (midnight)
# Version: 2.0 Enhanced
# ============================================================================

set -euo pipefail  # Exit on error, undefined vars, pipe failures

# Configuration
SCRIPT_NAME="StreamDB Enhanced Backend Maintenance"
SCRIPT_VERSION="2.0"
LOG_DIR="/var/log/streamdb-maintenance"
LOG_FILE="$LOG_DIR/enhanced-backend-maintenance-$(date +%Y%m%d-%H%M%S).log"
BACKUP_DIR="/var/backups/streamdb-maintenance"
LOCK_FILE="/var/run/streamdb-enhanced-backend-maintenance.lock"
CONFIG_BACKUP_DIR="$BACKUP_DIR/configs"
DB_BACKUP_DIR="$BACKUP_DIR/database"
MAX_LOG_FILES=30
NOTIFICATION_EMAIL="<EMAIL>"

# Enhanced Configuration
DISK_USAGE_THRESHOLD=85  # Alert if disk usage exceeds this percentage
MEMORY_USAGE_THRESHOLD=90  # Alert if memory usage exceeds this percentage
LOAD_AVERAGE_THRESHOLD=4.0  # Alert if 5-minute load average exceeds this
DB_BACKUP_RETENTION_DAYS=14
CONFIG_BACKUP_RETENTION_DAYS=30
MAINTENANCE_WINDOW_HOURS=2  # Maximum maintenance window in hours

# Service paths and configurations
STREAMDB_PATH="/var/www/streamdb_onl_usr/data/www/streamdb.online"
PM2_APP_NAME="streamdb-online"
MYSQL_DB="stream_db"
MYSQL_USER="stream_db_admin"
FASTPANEL_SERVICE="fastpanel"

# Critical services that must remain running
CRITICAL_SERVICES=("mysql" "fastpanel" "nginx" "ssh")

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# ============================================================================
# ENHANCED UTILITY FUNCTIONS
# ============================================================================

log() {
    local level="$1"
    shift
    local message="$*"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    local color=""
    
    case "$level" in
        "ERROR") color="$RED" ;;
        "WARN") color="$YELLOW" ;;
        "SUCCESS") color="$GREEN" ;;
        "INFO") color="$BLUE" ;;
        "DEBUG") color="$PURPLE" ;;
        *) color="$NC" ;;
    esac
    
    echo -e "${color}${timestamp} [${level}] ${message}${NC}" | tee -a "$LOG_FILE"
}

log_info() { log "INFO" "$@"; }
log_warn() { log "WARN" "$@"; }
log_error() { log "ERROR" "$@"; }
log_success() { log "SUCCESS" "$@"; }
log_debug() { log "DEBUG" "$@"; }

print_header() {
    echo -e "${CYAN}============================================================================${NC}"
    echo -e "${CYAN}$1${NC}"
    echo -e "${CYAN}============================================================================${NC}"
    log_info "$1"
}

print_section() {
    echo -e "\n${YELLOW}--- $1 ---${NC}"
    log_info "Starting: $1"
}

check_root() {
    if [[ $EUID -ne 0 ]]; then
        log_error "This script must be run as root"
        exit 1
    fi
}

create_lock() {
    if [[ -f "$LOCK_FILE" ]]; then
        local pid=$(cat "$LOCK_FILE")
        if kill -0 "$pid" 2>/dev/null; then
            log_error "Enhanced maintenance script is already running (PID: $pid)"
            exit 1
        else
            log_warn "Removing stale lock file"
            rm -f "$LOCK_FILE"
        fi
    fi
    echo $$ > "$LOCK_FILE"
    log_info "Created lock file: $LOCK_FILE"
}

remove_lock() {
    rm -f "$LOCK_FILE"
    log_info "Removed lock file"
}

setup_directories() {
    mkdir -p "$LOG_DIR" "$BACKUP_DIR" "$CONFIG_BACKUP_DIR" "$DB_BACKUP_DIR"
    chmod 755 "$LOG_DIR" "$BACKUP_DIR" "$CONFIG_BACKUP_DIR" "$DB_BACKUP_DIR"
    log_info "Created enhanced maintenance directories"
}

# ============================================================================
# ENHANCED MONITORING FUNCTIONS
# ============================================================================

check_system_resources() {
    print_section "Enhanced System Resource Monitoring"
    
    # Check disk usage
    local disk_usage=$(df / | awk 'NR==2 {print $5}' | sed 's/%//')
    log_info "Root filesystem usage: ${disk_usage}%"
    
    if [[ $disk_usage -gt $DISK_USAGE_THRESHOLD ]]; then
        log_warn "Disk usage (${disk_usage}%) exceeds threshold (${DISK_USAGE_THRESHOLD}%)"
        # Attempt cleanup
        cleanup_system_files
    fi
    
    # Check memory usage
    local memory_usage=$(free | awk 'NR==2{printf "%.0f", $3*100/$2}')
    log_info "Memory usage: ${memory_usage}%"
    
    if [[ $memory_usage -gt $MEMORY_USAGE_THRESHOLD ]]; then
        log_warn "Memory usage (${memory_usage}%) exceeds threshold (${MEMORY_USAGE_THRESHOLD}%)"
    fi
    
    # Check load average
    local load_avg=$(uptime | awk -F'load average:' '{print $2}' | awk '{print $2}' | sed 's/,//')
    log_info "5-minute load average: $load_avg"
    
    if (( $(echo "$load_avg > $LOAD_AVERAGE_THRESHOLD" | bc -l) )); then
        log_warn "Load average ($load_avg) exceeds threshold ($LOAD_AVERAGE_THRESHOLD)"
    fi
    
    # Check available inodes
    local inode_usage=$(df -i / | awk 'NR==2 {print $5}' | sed 's/%//')
    log_info "Inode usage: ${inode_usage}%"
    
    if [[ $inode_usage -gt 80 ]]; then
        log_warn "Inode usage (${inode_usage}%) is high"
    fi
}

monitor_critical_services() {
    print_section "Critical Service Health Monitoring"
    
    local failed_services=()
    
    for service in "${CRITICAL_SERVICES[@]}"; do
        if systemctl is-active --quiet "$service"; then
            log_success "Service $service is running"
        else
            log_error "Critical service $service is not running"
            failed_services+=("$service")
        fi
    done
    
    # Check PM2 processes
    if command -v pm2 >/dev/null 2>&1; then
        local pm2_status=$(pm2 jlist | jq -r ".[] | select(.name==\"$PM2_APP_NAME\") | .pm2_env.status" 2>/dev/null || echo "unknown")
        if [[ "$pm2_status" == "online" ]]; then
            log_success "PM2 application $PM2_APP_NAME is online"
        else
            log_error "PM2 application $PM2_APP_NAME status: $pm2_status"
            failed_services+=("pm2-$PM2_APP_NAME")
        fi
    fi
    
    if [[ ${#failed_services[@]} -gt 0 ]]; then
        log_error "Failed services detected: ${failed_services[*]}"
        return 1
    fi
    
    return 0
}

check_database_health() {
    print_section "Enhanced Database Health Check"
    
    # Check MySQL service
    if ! systemctl is-active --quiet mysql; then
        log_error "MySQL service is not running"
        return 1
    fi
    
    # Check database connectivity
    if ! mysql -u"$MYSQL_USER" -e "SELECT 1;" "$MYSQL_DB" >/dev/null 2>&1; then
        log_error "Cannot connect to database $MYSQL_DB"
        return 1
    fi
    
    # Check database size
    local db_size=$(mysql -u"$MYSQL_USER" -e "SELECT ROUND(SUM(data_length + index_length) / 1024 / 1024, 1) AS 'DB Size in MB' FROM information_schema.tables WHERE table_schema='$MYSQL_DB';" -s -N)
    log_info "Database size: ${db_size} MB"
    
    # Check for corrupted tables
    local corrupted_tables=$(mysql -u"$MYSQL_USER" -e "CHECK TABLE \`$MYSQL_DB\`.*;" "$MYSQL_DB" 2>/dev/null | grep -c "error" || echo "0")
    if [[ $corrupted_tables -gt 0 ]]; then
        log_warn "Found $corrupted_tables corrupted tables"
    else
        log_success "No corrupted tables found"
    fi
    
    # Check MySQL error log for recent errors
    local mysql_error_log="/var/log/mysql/error.log"
    if [[ -f "$mysql_error_log" ]]; then
        local recent_errors=$(tail -100 "$mysql_error_log" | grep -c "ERROR" || echo "0")
        log_info "Recent MySQL errors in log: $recent_errors"
    fi
    
    return 0
}

check_network_connectivity() {
    print_section "Network Connectivity Check"
    
    # Check reverse proxy connectivity
    if ping -c 3 91.208.197.50 >/dev/null 2>&1; then
        log_success "Reverse proxy server (91.208.197.50) is reachable"
    else
        log_error "Cannot reach reverse proxy server (91.208.197.50)"
    fi
    
    # Check external connectivity
    if ping -c 3 8.8.8.8 >/dev/null 2>&1; then
        log_success "External connectivity (8.8.8.8) is working"
    else
        log_warn "External connectivity check failed"
    fi
    
    # Check DNS resolution
    if nslookup streamdb.online >/dev/null 2>&1; then
        log_success "DNS resolution is working"
    else
        log_warn "DNS resolution check failed"
    fi
}

# ============================================================================
# ENHANCED BACKUP FUNCTIONS
# ============================================================================

create_enhanced_config_backup() {
    print_section "Enhanced Configuration Backup"
    
    local backup_timestamp=$(date +%Y%m%d-%H%M%S)
    local config_backup_file="$CONFIG_BACKUP_DIR/config-backup-$backup_timestamp.tar.gz"
    
    # Create comprehensive configuration backup
    tar -czf "$config_backup_file" \
        /etc/nginx/ \
        /etc/mysql/ \
        /etc/fastpanel/ \
        /etc/systemd/system/ \
        /etc/crontab \
        /etc/hosts \
        /etc/hostname \
        /etc/resolv.conf \
        /etc/ssh/sshd_config \
        /etc/ufw/ \
        "$STREAMDB_PATH"/.env \
        2>/dev/null || true
    
    if [[ -f "$config_backup_file" ]]; then
        log_success "Configuration backup created: $config_backup_file"
        
        # Verify backup integrity
        if tar -tzf "$config_backup_file" >/dev/null 2>&1; then
            log_success "Configuration backup integrity verified"
        else
            log_error "Configuration backup integrity check failed"
            return 1
        fi
    else
        log_error "Failed to create configuration backup"
        return 1
    fi
    
    return 0
}

create_enhanced_database_backup() {
    print_section "Enhanced Database Backup"

    local backup_timestamp=$(date +%Y%m%d-%H%M%S)
    local db_backup_file="$DB_BACKUP_DIR/database-backup-$backup_timestamp.sql.gz"

    # Create database backup with extended options
    if mysqldump -u"$MYSQL_USER" \
        --single-transaction \
        --routines \
        --triggers \
        --events \
        --hex-blob \
        --opt \
        "$MYSQL_DB" | gzip > "$db_backup_file"; then

        log_success "Database backup created: $db_backup_file"

        # Verify backup integrity
        if gunzip -t "$db_backup_file" 2>/dev/null; then
            log_success "Database backup integrity verified"

            # Check backup size
            local backup_size=$(du -h "$db_backup_file" | cut -f1)
            log_info "Database backup size: $backup_size"
        else
            log_error "Database backup integrity check failed"
            return 1
        fi
    else
        log_error "Failed to create database backup"
        return 1
    fi

    return 0
}

# ============================================================================
# ENHANCED MAINTENANCE FUNCTIONS
# ============================================================================

perform_security_updates() {
    print_section "Enhanced Security Updates"

    # Update package lists
    log_info "Updating package lists..."
    if apt update; then
        log_success "Package lists updated successfully"
    else
        log_error "Failed to update package lists"
        return 1
    fi

    # Apply security updates
    log_info "Applying security updates..."
    if unattended-upgrade -d; then
        log_success "Security updates applied successfully"
    else
        log_warn "Some security updates may have failed"
    fi

    # Check for available updates
    local available_updates=$(apt list --upgradable 2>/dev/null | wc -l)
    log_info "Available package updates: $((available_updates - 1))"

    return 0
}

perform_safe_system_updates() {
    print_section "Safe System Updates"

    # Get list of safe updates (excluding kernel and major packages)
    local safe_packages=$(apt list --upgradable 2>/dev/null | grep -v -E "(linux-|mysql-|nginx-|php)" | awk -F'/' '{print $1}' | tail -n +2)

    if [[ -n "$safe_packages" ]]; then
        log_info "Applying safe system updates..."
        echo "$safe_packages" | xargs apt install -y
        log_success "Safe system updates completed"
    else
        log_info "No safe system updates available"
    fi

    return 0
}

cleanup_system_files() {
    print_section "Enhanced System Cleanup"

    # Clean package cache
    log_info "Cleaning package cache..."
    apt autoremove -y
    apt autoclean

    # Clean temporary files
    log_info "Cleaning temporary files..."
    find /tmp -type f -atime +7 -delete 2>/dev/null || true
    find /var/tmp -type f -atime +7 -delete 2>/dev/null || true

    # Clean log files
    log_info "Rotating and cleaning log files..."
    logrotate -f /etc/logrotate.conf

    # Clean old kernels (keep current + 1 previous)
    log_info "Cleaning old kernels..."
    apt autoremove --purge -y

    # Clean application-specific files
    if [[ -d "$STREAMDB_PATH/logs" ]]; then
        find "$STREAMDB_PATH/logs" -name "*.log" -mtime +7 -delete 2>/dev/null || true
    fi

    # Clean PM2 logs
    if command -v pm2 >/dev/null 2>&1; then
        pm2 flush
        log_info "PM2 logs flushed"
    fi

    log_success "System cleanup completed"
}

#optimize_database() {
#    print_section "Database Optimization"

    # Optimize all tables in the database
#    log_info "Optimizing database tables..."
#    mysql -u"$MYSQL_USER" -e "
#        SELECT CONCAT('OPTIMIZE TABLE \`', table_schema, '\`.\`', table_name, '\`;') AS statement
#        FROM information_schema.tables
 #       WHERE table_schema = '$MYSQL_DB'
 #       AND table_type = 'BASE TABLE';" -s -N | mysql -u"$MYSQL_USER" "$MYSQL_DB"

  #  log_success "Database optimization completed"

    # Analyze tables for query optimization
   # log_info "Analyzing database tables..."
   # mysql -u"$MYSQL_USER" -e "
   #     SELECT CONCAT('ANALYZE TABLE \`', table_schema, '\`.\`', table_name, '\`;') AS statement
   #     FROM information_schema.tables
  #      WHERE table_schema = '$MYSQL_DB'
   #     AND table_type = 'BASE TABLE';" -s -N | mysql -u"$MYSQL_USER" "$MYSQL_DB"

   # log_success "Database analysis completed"
#}

# ============================================================================
# ENHANCED RECOVERY FUNCTIONS
# ============================================================================

restore_service_states() {
    print_section "Service State Restoration"

    # Ensure all critical services are running
    for service in "${CRITICAL_SERVICES[@]}"; do
        if ! systemctl is-active --quiet "$service"; then
            log_warn "Attempting to restart $service"
            if systemctl restart "$service"; then
                log_success "Successfully restarted $service"
            else
                log_error "Failed to restart $service"
            fi
        fi
    done

    # Ensure PM2 application is running
    if command -v pm2 >/dev/null 2>&1; then
        local pm2_status=$(pm2 jlist | jq -r ".[] | select(.name==\"$PM2_APP_NAME\") | .pm2_env.status" 2>/dev/null || echo "unknown")
        if [[ "$pm2_status" != "online" ]]; then
            log_warn "Attempting to restart PM2 application $PM2_APP_NAME"
            if pm2 restart "$PM2_APP_NAME"; then
                log_success "Successfully restarted PM2 application"
            else
                log_error "Failed to restart PM2 application"
            fi
        fi
    fi
}

cleanup_old_backups() {
    print_section "Backup Cleanup"

    # Clean old database backups
    find "$DB_BACKUP_DIR" -name "database-backup-*.sql.gz" -mtime +$DB_BACKUP_RETENTION_DAYS -delete 2>/dev/null || true
    log_info "Cleaned database backups older than $DB_BACKUP_RETENTION_DAYS days"

    # Clean old configuration backups
    find "$CONFIG_BACKUP_DIR" -name "config-backup-*.tar.gz" -mtime +$CONFIG_BACKUP_RETENTION_DAYS -delete 2>/dev/null || true
    log_info "Cleaned configuration backups older than $CONFIG_BACKUP_RETENTION_DAYS days"

    # Clean old log files
    find "$LOG_DIR" -name "enhanced-backend-maintenance-*.log" -mtime +$MAX_LOG_FILES -delete 2>/dev/null || true
    log_info "Cleaned maintenance logs older than $MAX_LOG_FILES days"
}

# ============================================================================
# MAIN EXECUTION FUNCTION
# ============================================================================

main() {
    # Setup
    check_root
    setup_directories
    create_lock

    # Trap to ensure cleanup on exit
    trap 'remove_lock; exit' INT TERM EXIT

    print_header "$SCRIPT_NAME v$SCRIPT_VERSION - $(date)"
    log_info "Starting enhanced maintenance on backend1maindb (***********)"

    local start_time=$(date +%s)

    # Pre-maintenance checks
    check_system_resources

    if ! monitor_critical_services; then
        log_error "Critical service failures detected - aborting maintenance"
        exit 1
    fi

    check_database_health
    check_network_connectivity

    # Create backups
    if ! create_enhanced_config_backup; then
        log_error "Configuration backup failed - aborting maintenance"
        exit 1
    fi

    if ! create_enhanced_database_backup; then
        log_error "Database backup failed - aborting maintenance"
        exit 1
    fi

    # Perform maintenance
    perform_security_updates
    perform_safe_system_updates
    cleanup_system_files
    #optimize_database

    # Post-maintenance tasks
    restore_service_states
    cleanup_old_backups

    # Final health check
    sleep 10  # Give services time to stabilize
    if monitor_critical_services && check_database_health; then
        log_success "All services are healthy after maintenance"
    else
        log_warn "Some services may need attention after maintenance"
    fi

    local end_time=$(date +%s)
    local duration=$((end_time - start_time))

    print_header "Enhanced Maintenance Completed Successfully"
    log_success "Backend server maintenance completed in ${duration} seconds at $(date)"

    # Remove lock file
    remove_lock
}

# Execute main function
main "$@"
