# Cloudflare Configuration Documentation Template

**Domain:** streamdb.online  
**Documentation Date:** 21st July 2025  
**Account Email:** <EMAIL>

## DNS Configuration

### A Records
| Name | IPv4 Address | Proxy Status | TTL |
|------|-------------|--------------|-----|
| @ | ************* | Proxied | Auto |
| www | ************* | Proxied | Auto |
| fastpanel | ************* | Proxied | Auto |

### CNAME Records
| Name | Target | Proxy Status | TTL |
|------|--------|--------------|-----|
| [FILL IN] | [FILL IN] | [Proxied/DNS Only] | [Auto/Custom] |

### MX Records
| Name | Mail Server | Priority | TTL |
|------|-------------|----------|-----|
| streamdb.online | eforward3.registrar-servers.com | 10 | Auto |
| streamdb.online | eforward2.registrar-servers.com | 10 | Auto |
| streamdb.online | eforward1.registrar-servers.com | 10 | Auto |
| streamdb.online | eforward4.registrar-servers.com | 15 | Auto |
| streamdb.online | eforward5.registrar-servers.com | 20 | Auto |

### Other Records (TXT, AAAA, etc.)
| Type | Name | Content | TTL |
|------|------|---------|-----|
| NS | streamdb.online | dns2.registrar-servers.com | Auto |
| NS | streamdb.online | dns1.registrar-servers.com | Auto |
| TXT | streamdb.online | "v=spf1 include:spf.efwd.registrar-servers.com ~all" | Auto |

## SSL/TLS Configuration

### SSL/TLS Encryption Mode
- **Current Setting:** Full (strict)
- **Reason for Choice:** Current setting works so keep this

### Edge Certificates
- **Universal SSL:** Enabled
- **Always Use HTTPS:** Enabled
- **HTTP Strict Transport Security (HSTS):** Enabled
  - Max Age Header: [FILL IN IF ENABLED]
  - Include Subdomains: [Yes/No]
  - Preload: [Yes/No]
- **Minimum TLS Version:** 1.2
- **Opportunistic Encryption:** Enabled
- **TLS 1.3:** Enabled
- **Automatic HTTPS Rewrites:** Enabled
- **Certificate Transparency Monitoring:** Disabled

### Origin Certificates
- **Origin Certificate:** Present
- **Certificate Details:** *.streamdb.online, streamdb.online (2 hosts)

## Security Configuration

### Security Level
- **Current Setting:** [Essentially Off/Low/Medium/High/I'm Under Attack]

### Bot Fight Mode
- **Status:** Enabled
- **Super Bot Fight Mode:** [Enabled/Disabled - Pro+ feature]

### Challenge Passage
- **Challenge Passage:** [FILL IN DURATION]

### Browser Integrity Check
- **Status:** [Enabled/Disabled]

### Privacy Pass
- **Status:** [Enabled/Disabled]

### Security Events
- **Recent Events:** [FILL IN ANY NOTABLE SECURITY EVENTS]

## Firewall Configuration

### Firewall Rules
| Rule Name | Expression | Action | Priority |
|-----------|------------|--------|----------|
| [FILL IN] | [FILL IN] | [Allow/Block/Challenge/etc.] | [FILL IN] |

### Rate Limiting Rules
| Rule Name | Characteristics | Period | Requests | Action |
|-----------|----------------|---------|----------|--------|
| [FILL IN] | [FILL IN] | [FILL IN] | [FILL IN] | [FILL IN] |

### Access Rules
| Rule Name | Configuration | Action |
|-----------|---------------|--------|
| [FILL IN] | [FILL IN] | [Allow/Block/Challenge] |

### User Agent Blocking
- **Blocked User Agents:** [FILL IN IF ANY]

### Zone Lockdown
- **Lockdown Rules:** [FILL IN IF ANY]

## Speed Configuration

### Auto Minify
- **JavaScript:** [Enabled/Disabled]
- **CSS:** [Enabled/Disabled]
- **HTML:** [Enabled/Disabled]

### Brotli Compression
- **Status:** [Enabled/Disabled]

### Early Hints
- **Status:** Disabled

### HTTP/2
- **Status:** Enabled

### HTTP/3 (with QUIC)
- **Status:** [Enabled/Disabled]

### 0-RTT Connection Resumption
- **Status:** Disabled

### IPv6 Compatibility
- **Status:** [Enabled/Disabled]

### Pseudo IPv4
- **Status:** [Off/Add header/Overwrite headers]

### IP Geolocation
- **Status:** [Enabled/Disabled]

### Maximum Upload Size
- **Size:** [100MB/200MB/500MB - varies by plan]

## Caching Configuration

### Caching Level
- **Current Setting:** Standard

### Browser Cache TTL
- **Setting:** [Respect Existing Headers/Custom duration]

### Always Online
- **Status:** Disabled

### Development Mode
- **Status:** Disabled

## Page Rules

### Active Page Rules
| URL Pattern | Settings | Priority |
|-------------|----------|----------|
| [FILL IN] | [FILL IN ALL SETTINGS] | [FILL IN] |

**Example Settings to Document:**
- Always Use HTTPS
- SSL: Full/Flexible/etc.
- Cache Level
- Edge Cache TTL
- Browser Cache TTL
- Security Level
- Disable Apps
- Disable Performance
- Disable Railgun
- Disable Zaraz
- Email Obfuscation
- Server Side Excludes
- Smart Errors
- WAF

## Analytics and Monitoring

### Analytics
- **Web Analytics:** [Enabled/Disabled]
- **Zone Analytics:** [Available data retention period]

### Notifications
- **Email Notifications:** [List configured notifications]
- **Webhook Notifications:** [List configured webhooks]

## Additional Services

### Zaraz (Tag Management)
- **Status:** [Enabled/Disabled]
- **Configured Tools:** [FILL IN IF ENABLED]

### Cloudflare Apps
- **Installed Apps:** [FILL IN IF ANY]

### Workers
- **Deployed Workers:** [FILL IN IF ANY]
- **Routes:** [FILL IN WORKER ROUTES]

### Load Balancing
- **Load Balancers:** [FILL IN IF ANY]

### Argo Smart Routing
- **Status:** [Enabled/Disabled - Paid feature]

### Argo Tunnel
- **Tunnels:** [FILL IN IF ANY]

## Account Settings

### API Tokens
- **Active Tokens:** [FILL IN TOKEN NAMES - NOT VALUES]
- **Permissions:** [FILL IN PERMISSIONS FOR EACH TOKEN]

### Audit Logs
- **Recent Important Changes:** [FILL IN ANY RECENT CONFIGURATION CHANGES]

## Backup Information

### Configuration Export
- **Last Backup Date:** [FILL IN]
- **Backup Method:** [Manual documentation/API export/Third-party tool]

### Recovery Contacts
- **Primary Contact:** [FILL IN]
- **Secondary Contact:** [FILL IN]
- **Account Recovery Email:** [FILL IN]

## Migration Notes

### Critical Settings for Migration
1. **DNS Records:** All A, CNAME, MX records must be replicated exactly
2. **SSL Mode:** Must match current setting to avoid SSL errors
3. **Page Rules:** Critical for proper routing and caching
4. **Firewall Rules:** Important for security
5. **API Tokens:** Will need to be recreated if using automation

### Pre-Migration Checklist
- [ ] Lower TTL on all DNS records to 300 seconds (5 minutes)
- [ ] Document all current settings using this template
- [ ] Prepare new server IP addresses
- [ ] Plan maintenance window
- [ ] Notify users of potential brief downtime

### Migration Steps
1. Update A record for @ to point to new reverse proxy server IP
2. Update A record for www to point to new reverse proxy server IP
3. Monitor traffic switch (should happen within 5 minutes due to low TTL)
4. Verify all functionality works correctly
5. Gradually increase TTL back to normal values (usually 1 hour = 3600 seconds)

## Instructions for Filling This Template

1. **Login to Cloudflare Dashboard:** https://dash.cloudflare.com
2. **Select your domain:** streamdb.online
3. **Go through each section systematically:**
   - DNS: Copy all records exactly
   - SSL/TLS: Note all settings in each sub-tab
   - Security: Document all security configurations
   - Speed: Note all optimization settings
   - Caching: Document cache configurations
   - Page Rules: Copy all rules with their exact settings
   - Analytics: Note what's enabled
4. **Take screenshots** of complex configurations
5. **Save this completed template** as your Cloudflare backup documentation

## Important Notes

- **Never share API keys or tokens** in documentation
- **Keep this documentation updated** when you make changes
- **Store securely** as it contains sensitive configuration information
- **Test all settings** after any migration to ensure they work correctly
