#!/usr/bin/env node

/**
 * Socket-Only Database Connection Handler for SEO Scripts
 * Mirrors the exact production server connection method for security and consistency
 */

import dotenv from 'dotenv';
import mysql from 'mysql2/promise';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Load environment variables
dotenv.config();

/**
 * Socket-only database connection handler matching production server configuration
 */
export class DatabaseConnectionHandler {
  constructor() {
    this.connection = null;
    this.connectionConfig = null;
  }

  /**
   * Get socket connection configurations (production-safe, no TCP)
   * Mirrors server/config/database.js configuration exactly
   */
  getSocketConfigurations() {
    // Base configuration for single connections (testing)
    const baseConnectionConfig = {
      user: process.env.DB_USER || 'stream_db_admin',
      password: process.env.DB_PASSWORD || '',
      database: process.env.DB_NAME || 'stream_db',
      charset: 'utf8mb4',
      timezone: '+00:00',
      connectTimeout: 60000,
      ssl: false,
      multipleStatements: false
    };

    // Pool configuration (for production use)
    const basePoolConfig = {
      ...baseConnectionConfig,
      connectionLimit: 5,
      queueLimit: 0,
      acquireTimeout: 60000,
      timeout: 60000
    };

    // Socket connection configurations in order of preference
    const socketPaths = [
      process.env.DB_SOCKET || '/var/run/mysqld/mysqld.sock',
      '/var/run/mysqld/mysqld.sock',
      '/tmp/mysql.sock',
      '/var/lib/mysql/mysql.sock',
      '/run/mysqld/mysqld.sock'
    ];

    return socketPaths.map((socketPath, index) => ({
      connection: { ...baseConnectionConfig, socketPath },
      pool: { ...basePoolConfig, socketPath },
      name: index === 0 ? 'Production Socket (Primary)' : `Alternative Socket ${index}`,
      socketPath
    }));
  }

  /**
   * Test a socket connection configuration
   */
  async testSocketConnection(config) {
    let testConnection = null;
    try {
      console.log(`🔗 Testing ${config.name} (${config.socketPath})...`);

      // First check if socket file exists
      if (!fs.existsSync(config.socketPath)) {
        throw new Error(`Socket file not found: ${config.socketPath}`);
      }

      // Check socket file permissions
      const stats = fs.statSync(config.socketPath);
      if (!stats.isSocket()) {
        throw new Error(`Not a socket file: ${config.socketPath}`);
      }

      // Use the clean connection config (without pool-specific options)
      testConnection = await mysql.createConnection(config.connection);

      // Test basic connectivity
      await testConnection.execute('SELECT 1 as test');

      // Test database access
      const [dbResult] = await testConnection.execute('SELECT DATABASE() as current_db');

      // Test table access
      const [tables] = await testConnection.execute(`SHOW TABLES LIKE 'content'`);

      if (tables.length === 0) {
        throw new Error('Content table not found');
      }

      console.log(`✅ ${config.name} successful - Database: ${dbResult[0].current_db}`);
      return true;

    } catch (error) {
      console.log(`❌ ${config.name} failed: ${error.message}`);
      return false;
    } finally {
      if (testConnection) {
        try {
          await testConnection.end();
        } catch (closeError) {
          // Ignore close errors
        }
      }
    }
  }

  /**
   * Establish socket connection with fallback to alternative socket paths
   */
  async connect() {
    if (this.connection) {
      return this.connection;
    }

    const socketConfigurations = this.getSocketConfigurations();

    console.log('🔄 Attempting socket-based database connection (production method)...');

    for (const config of socketConfigurations) {
      try {
        const isWorking = await this.testSocketConnection(config);

        if (isWorking) {
          // Store the working configuration
          this.connectionConfig = config;

          // Create the actual connection pool using clean pool config
          this.connection = mysql.createPool(config.pool);

          console.log(`✅ Socket connection established: ${config.name}`);
          console.log(`✓ Using MySQL socket connection for SEO scripts (matches production)`);
          return this.connection;
        }

      } catch (error) {
        console.log(`❌ ${config.name} failed: ${error.message}`);
        continue;
      }
    }

    throw new Error('All socket connection methods failed. Check MySQL socket availability and permissions.');
  }

  /**
   * Execute query with automatic connection handling
   */
  async execute(query, params = []) {
    try {
      const connection = await this.connect();
      const [rows] = await connection.execute(query, params);
      return rows;
    } catch (error) {
      console.error('Query execution failed:', error.message);
      
      // Reset connection on failure and retry once
      if (this.connection) {
        try {
          await this.connection.end();
        } catch (e) {
          // Ignore cleanup errors
        }
        this.connection = null;
        
        // Retry once with fresh connection
        try {
          const connection = await this.connect();
          const [rows] = await connection.execute(query, params);
          return rows;
        } catch (retryError) {
          throw new Error(`Database query failed after retry: ${retryError.message}`);
        }
      }
      
      throw error;
    }
  }

  /**
   * Close connection gracefully
   */
  async close() {
    if (this.connection) {
      try {
        await this.connection.end();
        this.connection = null;
        this.connectionConfig = null;
        console.log('✅ Database connection closed gracefully');
      } catch (error) {
        console.log('⚠️ Error closing database connection:', error.message);
        // Force close if graceful close fails
        this.forceClose();
      }
    }
  }

  /**
   * Force close connection immediately (for build processes)
   */
  forceClose() {
    if (this.connection) {
      try {
        // For connection pools, we need to destroy all connections
        if (typeof this.connection.end === 'function') {
          // Try graceful close first
          this.connection.end(() => {
            this.connection = null;
            this.connectionConfig = null;
          });
        } else {
          // Fallback for other connection types
          this.connection = null;
          this.connectionConfig = null;
        }
        console.log('✅ Database connection force closed');
      } catch (error) {
        // Even if force close fails, clear the reference
        this.connection = null;
        this.connectionConfig = null;
        console.log('⚠️ Error force closing database connection, reference cleared');
      }
    }
  }

  /**
   * Get connection status
   */
  getStatus() {
    return {
      connected: !!this.connection,
      config: this.connectionConfig ? {
        method: 'socket',
        socketPath: this.connectionConfig.socketPath,
        database: this.connectionConfig.pool.database,
        user: this.connectionConfig.pool.user
      } : null
    };
  }
}

// Create singleton instance
const dbHandler = new DatabaseConnectionHandler();

export default dbHandler;

/**
 * Convenience function for direct usage
 */
export async function getDatabaseConnection() {
  return await dbHandler.connect();
}

/**
 * Convenience function for executing queries
 */
export async function executeQuery(query, params = []) {
  return await dbHandler.execute(query, params);
}

/**
 * Test all socket connection methods (for diagnostics)
 */
export async function testAllSocketConnections() {
  const handler = new DatabaseConnectionHandler();
  const configurations = handler.getSocketConfigurations();

  console.log('🧪 Testing all socket connection methods (production-safe)...\n');

  const results = [];

  for (const config of configurations) {
    const result = await handler.testSocketConnection(config);
    results.push({
      name: config.name,
      success: result,
      config: {
        method: 'socket',
        path: config.socketPath
      }
    });
  }

  console.log('\n📊 Socket Connection Test Results:');
  results.forEach(result => {
    const status = result.success ? '✅' : '❌';
    console.log(`${status} ${result.name}: ${result.config.method} (${result.config.path})`);
  });

  const workingConnections = results.filter(r => r.success);
  if (workingConnections.length > 0) {
    console.log(`\n🎉 Found ${workingConnections.length} working socket connection(s) - SEO scripts will work!`);
  } else {
    console.log('\n⚠️ No socket connections working. Check MySQL socket file permissions.');
  }

  return results;
}
