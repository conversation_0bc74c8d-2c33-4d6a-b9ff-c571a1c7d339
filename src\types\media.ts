
export type MediaType = "movie" | "series" | "requested";

export interface Episode {
  id: string;
  title: string;
  season: number;
  episode: number;
  description: string;
  videoLink: string; // DEPRECATED: Use secureVideoLinks instead
  secureVideoLinks?: string; // Encrypted/encoded video links for security
  runtime?: string;
  airDate?: string;
  thumbnailUrl?: string;
  createdAt: string;
  updatedAt: string;
}

export interface Season {
  id: string;
  seasonNumber: number;
  title?: string;
  description?: string;
  episodes: Episode[];
  posterUrl?: string;
  createdAt: string;
  updatedAt: string;
}

export interface MediaItem {
  id: string;
  title: string;
  description: string;
  year: number;
  genres: string[];
  quality?: string[];
  type: MediaType;
  category?: string; // New category field for content classification
  section_ids?: number[]; // New field for multiple sections support
  image: string;
  coverImage: string; // for large carousel background
  createdAt: string; // ISO date string for when content was added

  // Enhanced fields for comprehensive content management
  tmdbId?: string;
  languages?: string[];
  posterUrl?: string;
  thumbnailUrl?: string;
  videoLinks?: string; // DEPRECATED: Use secureVideoLinks instead
  secureVideoLinks?: string; // Encrypted/encoded video links for security
  quality?: string[];
  qualityLabel?: string; // New field for homepage quality display
  customQualityLabel?: string; // New field for custom quality labels
  tags?: string;
  imdbRating?: string;
  runtime?: string;
  studio?: string;
  audioTracks?: string[];
  trailer?: string;
  subtitleUrl?: string;
  isPublished?: boolean;
  isFeatured?: boolean;
  addToCarousel?: boolean;
  updatedAt?: string;

  // Web series specific fields
  seasons?: Season[];
  totalSeasons?: number;
  totalEpisodes?: number;
}

// Enhanced content item interface for admin management
export interface ContentItem {
  id: string;
  title: string;
  type: string;
  category?: string; // New category field for content classification
  section_ids?: number[]; // New field for multiple sections support
  year: number;
  genre: string[];
  status: string;
  featured: boolean;
  carousel: boolean;
  imdbRating: string;
  runtime: string;
  studio: string;
  description?: string;
  posterUrl?: string;
  thumbnailUrl?: string;
  videoLinks?: string;
  secureVideoLinks?: string;
  languages?: string[];
  quality?: string[];
  qualityLabel?: string; // New field for homepage quality display
  customQualityLabel?: string; // New field for custom quality labels
  tags?: string;
  audioTracks?: string[];
  trailer?: string;
  subtitleUrl?: string;
  seasons?: Season[];
  createdAt: string;
  updatedAt: string;
}
