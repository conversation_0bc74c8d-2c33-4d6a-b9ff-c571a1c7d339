# 🔧 StreamDB Enhanced Maintenance Troubleshooting Guide

## 📋 OVERVIEW

This comprehensive troubleshooting guide covers common issues, solutions, and advanced configuration options for the StreamDB Enhanced Automated Maintenance System.

---

## 🚨 EMERGENCY PROCEDURES

### Immediate Actions for Critical Issues

**Stop All Maintenance Scripts:**
```bash
# Kill any running maintenance processes
pkill -f "enhanced.*maintenance.sh"

# Remove lock files
rm -f /var/run/streamdb-enhanced-*-maintenance.lock

# Disable cron jobs temporarily
mv /etc/cron.d/streamdb-enhanced-maintenance /etc/cron.d/streamdb-enhanced-maintenance.disabled

# Restart cron service
systemctl restart cron
```

**Emergency Service Restoration:**
```bash
# Backend Server Emergency Restoration
systemctl restart mysql
systemctl restart fastpanel
pm2 restart streamdb-online

# Reverse Proxy Emergency Restoration
nginx -t && systemctl restart nginx
systemctl restart ufw
```

**Emergency Backup Restoration:**
```bash
# Restore latest configuration backup (Backend)
cd /var/backups/streamdb-maintenance/configs
LATEST_BACKUP=$(ls -t config-backup-*.tar.gz | head -1)
tar -xzf "$LATEST_BACKUP" -C / --overwrite

# Restore latest nginx configuration (Proxy)
cd /var/backups/streamdb-maintenance/nginx-configs
LATEST_NGINX_BACKUP=$(ls -t nginx-config-backup-*.tar.gz | head -1)
tar -xzf "$LATEST_NGINX_BACKUP" -C / --overwrite
nginx -t && systemctl reload nginx
```

---

## 🔍 COMMON ISSUES AND SOLUTIONS

### Issue 1: Script Permission Errors

**Symptoms:**
- "Permission denied" errors in logs
- Scripts fail to execute via cron

**Solutions:**
```bash
# Fix script permissions
chmod +x /usr/local/bin/enhanced-*-maintenance.sh
chown root:root /usr/local/bin/enhanced-*-maintenance.sh

# Fix directory permissions
chmod 755 /var/log/streamdb-maintenance
chmod 755 /var/backups/streamdb-maintenance
chmod 755 /etc/streamdb-maintenance

# Fix log file permissions
chown root:root /var/log/streamdb-maintenance/*.log
chmod 644 /var/log/streamdb-maintenance/*.log
```

### Issue 2: Database Connection Failures

**Symptoms:**
- "Cannot connect to database" errors
- Database backup failures

**Diagnosis:**
```bash
# Test database connectivity
mysql -u stream_db_admin -e "SELECT 1;" stream_db

# Check MySQL service status
systemctl status mysql

# Check MySQL error logs
tail -50 /var/log/mysql/error.log
```

**Solutions:**
```bash
# Restart MySQL service
systemctl restart mysql

# Check MySQL configuration
mysql -u root -e "SHOW GRANTS FOR 'stream_db_admin'@'localhost';"

# Verify database exists
mysql -u root -e "SHOW DATABASES;" | grep stream_db

# Reset MySQL password if needed
mysql -u root -e "ALTER USER 'stream_db_admin'@'localhost' IDENTIFIED BY 'your_password';"
```

### Issue 3: Nginx Configuration Errors

**Symptoms:**
- "nginx: configuration file test failed" errors
- HTTP/HTTPS connectivity failures

**Diagnosis:**
```bash
# Test nginx configuration
nginx -t

# Check nginx error logs
tail -50 /var/log/nginx/error.log

# Check nginx service status
systemctl status nginx
```

**Solutions:**
```bash
# Restore nginx configuration from backup
cd /var/backups/streamdb-maintenance/nginx-configs
LATEST_BACKUP=$(ls -t nginx-config-backup-*.tar.gz | head -1)
tar -xzf "$LATEST_BACKUP" -C / --overwrite

# Test and reload configuration
nginx -t && systemctl reload nginx

# Check specific configuration files
nginx -T | grep -A 10 -B 10 "error"
```

### Issue 4: SSL Certificate Issues

**Symptoms:**
- SSL certificate warnings in logs
- HTTPS connectivity failures

**Diagnosis:**
```bash
# Check certificate expiration
openssl x509 -in /etc/ssl/certs/cloudflare-origin.pem -noout -enddate

# Verify certificate and key match
CERT_HASH=$(openssl x509 -in /etc/ssl/certs/cloudflare-origin.pem -noout -modulus | openssl md5)
KEY_HASH=$(openssl rsa -in /etc/ssl/private/cloudflare-origin.key -noout -modulus | openssl md5)
echo "Certificate hash: $CERT_HASH"
echo "Key hash: $KEY_HASH"

# Test SSL connectivity
openssl s_client -connect localhost:443 -servername streamdb.online
```

**Solutions:**
```bash
# Backup current certificates
cp /etc/ssl/certs/cloudflare-origin.pem /etc/ssl/certs/cloudflare-origin.pem.backup
cp /etc/ssl/private/cloudflare-origin.key /etc/ssl/private/cloudflare-origin.key.backup

# Update certificates (replace with your new certificates)
# Upload new certificate and key files
# Verify permissions
chmod 644 /etc/ssl/certs/cloudflare-origin.pem
chmod 600 /etc/ssl/private/cloudflare-origin.key
chown root:root /etc/ssl/certs/cloudflare-origin.pem
chown root:root /etc/ssl/private/cloudflare-origin.key

# Test and reload nginx
nginx -t && systemctl reload nginx
```

### Issue 5: High Resource Usage During Maintenance

**Symptoms:**
- Server becomes unresponsive during maintenance
- High CPU/memory usage alerts

**Diagnosis:**
```bash
# Monitor resource usage during maintenance
htop
iotop
nethogs

# Check maintenance logs for resource warnings
grep -i "threshold\|usage" /var/log/streamdb-maintenance/enhanced-*-maintenance-*.log
```

**Solutions:**
```bash
# Adjust resource thresholds in configuration
nano /etc/streamdb-maintenance/backend-config.conf
# Lower thresholds:
# DISK_USAGE_THRESHOLD=75
# MEMORY_USAGE_THRESHOLD=80
# LOAD_AVERAGE_THRESHOLD=3.0

nano /etc/streamdb-maintenance/proxy-config.conf
# Lower thresholds:
# DISK_USAGE_THRESHOLD=75
# MEMORY_USAGE_THRESHOLD=80
# LOAD_AVERAGE_THRESHOLD=1.5

# Implement maintenance throttling
# Add sleep commands between intensive operations in scripts
```

### Issue 6: PM2 Application Failures

**Symptoms:**
- StreamDB application not responding
- PM2 process crashes during maintenance

**Diagnosis:**
```bash
# Check PM2 status
pm2 list
pm2 logs streamdb-online

# Check application logs
tail -50 /var/www/streamdb_onl_usr/data/www/streamdb.online/logs/app.log
```

**Solutions:**
```bash
# Restart PM2 application
pm2 restart streamdb-online

# Reload PM2 configuration
pm2 reload streamdb-online

# Check PM2 startup script
pm2 startup
pm2 save

# Verify application configuration
cd /var/www/streamdb_onl_usr/data/www/streamdb.online
npm test  # If test script exists
```

### Issue 7: Backup Failures

**Symptoms:**
- "Backup failed" errors in logs
- Missing backup files

**Diagnosis:**
```bash
# Check backup directory permissions
ls -la /var/backups/streamdb-maintenance/

# Check available disk space
df -h /var/backups/

# Test backup commands manually
mysqldump -u stream_db_admin --single-transaction stream_db | gzip > test-backup.sql.gz
tar -czf test-config-backup.tar.gz /etc/nginx/
```

**Solutions:**
```bash
# Fix backup directory permissions
chown -R root:root /var/backups/streamdb-maintenance/
chmod -R 755 /var/backups/streamdb-maintenance/

# Clean old backups to free space
find /var/backups/streamdb-maintenance/ -name "*.tar.gz" -mtime +30 -delete
find /var/backups/streamdb-maintenance/ -name "*.sql.gz" -mtime +14 -delete

# Verify MySQL dump permissions
mysql -u stream_db_admin -e "SHOW GRANTS;" | grep -i "select\|lock"
```

---

## ⚙️ ADVANCED CONFIGURATION

### Custom Threshold Configuration

**Backend Server Thresholds:**
```bash
# Edit backend configuration
nano /etc/streamdb-maintenance/backend-config.conf

# Example advanced configuration:
DISK_USAGE_THRESHOLD=80                    # Disk usage alert threshold
MEMORY_USAGE_THRESHOLD=85                  # Memory usage alert threshold
LOAD_AVERAGE_THRESHOLD=3.5                 # Load average alert threshold
DB_BACKUP_RETENTION_DAYS=21               # Database backup retention
CONFIG_BACKUP_RETENTION_DAYS=45           # Configuration backup retention
MAINTENANCE_WINDOW_HOURS=3                # Maximum maintenance duration
NOTIFICATION_EMAIL="<EMAIL>" # Alert email address
```

**Reverse Proxy Thresholds:**
```bash
# Edit proxy configuration
nano /etc/streamdb-maintenance/proxy-config.conf

# Example advanced configuration:
DISK_USAGE_THRESHOLD=80                    # Disk usage alert threshold
MEMORY_USAGE_THRESHOLD=85                  # Memory usage alert threshold
LOAD_AVERAGE_THRESHOLD=1.8                 # Load average alert threshold
SSL_CERT_EXPIRY_WARNING_DAYS=45           # SSL certificate warning period
NGINX_CONFIG_BACKUP_RETENTION_DAYS=45     # Nginx backup retention
BACKEND_HEALTH_TIMEOUT=15                 # Backend health check timeout
MAINTENANCE_WINDOW_HOURS=2                # Maximum maintenance duration
```

### Custom Maintenance Schedules

**Alternative Scheduling Options:**
```bash
# Edit cron configuration for different schedules
nano /etc/cron.d/streamdb-enhanced-maintenance

# Weekly maintenance on Sunday at 2 AM
0 2 * * 0 root /usr/local/bin/enhanced-backend-maintenance.sh

# Bi-weekly maintenance (every other Thursday)
0 0 * * 4 root [ $(expr $(date +\%W) \% 2) -eq 0 ] && /usr/local/bin/enhanced-backend-maintenance.sh

# Monthly maintenance (first Thursday of month)
0 0 1-7 * 4 root /usr/local/bin/enhanced-backend-maintenance.sh
```

### Email Notification Setup

**Configure Email Alerts:**
```bash
# Install mail utilities
apt install -y mailutils postfix

# Configure postfix for external SMTP (optional)
dpkg-reconfigure postfix

# Test email functionality
echo "Test email from StreamDB maintenance" | mail -s "Test" <EMAIL>

# Add email notifications to scripts
# Edit scripts to include email alerts for critical failures
```
