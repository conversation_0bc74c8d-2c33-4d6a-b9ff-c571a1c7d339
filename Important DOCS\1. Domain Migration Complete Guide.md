# 🌐 StreamDB Domain Migration & Multi-Domain Setup Guide

## 📋 OVERVIEW

This comprehensive guide provides step-by-step instructions for migrating your StreamDB website from `streamdb.online` to a new domain and setting up multi-domain support with 301 redirects for maximum uptime and redundancy.

### Current Infrastructure
```
Client Browser
     ↓
Cloudflare (Free Plan) - streamdb.online
     ↓
1st Offshore Reverse Proxy VPS: ************* (<PERSON>inx)
     ↓
2nd Offshore Backend VPS: *********** (FastPanel2 + MySQL + Node.js)
```

### Current Configuration Summary
- **Primary Domain**: streamdb.online
- **Database**: stream_db (MySQL on localhost)
- **Backend Path**: `/var/www/streamdb_onl_usr/data/www/streamdb.online`
- **Node.js Port**: 3001
- **FastPanel Port**: 8888
- **SSL**: Managed by Cloudflare

---

## 🎯 SCENARIO 1: MIGRATE TO NEW DOMAIN

### Phase 1: Prepare New Domain

#### Step 1.1: Purchase and Setup New Domain
1. **Purchase your new domain** (e.g., `newstreamdb.com`)
2. **Add domain to Cloudflare**:
   - Login to Cloudflare Dashboard: https://dash.cloudflare.com/
   - Click "Add a Site"
   - Enter your new domain name
   - Select "Free Plan"
   - Follow nameserver setup instructions

#### Step 1.2: Configure DNS for New Domain
**In Cloudflare DNS settings for your NEW domain:**

1. **Main Website Record:**
   ```
   Type: A
   Name: @
   IPv4 address: *************
   Proxy status: ✅ Proxied (Orange Cloud)
   TTL: Auto
   ```

2. **WWW Subdomain:**
   ```
   Type: A
   Name: www
   IPv4 address: *************
   Proxy status: ✅ Proxied (Orange Cloud)
   TTL: Auto
   ```

3. **FastPanel Subdomain:**
   ```
   Type: A
   Name: fastpanel
   IPv4 address: *************
   Proxy status: ✅ Proxied (Orange Cloud)
   TTL: Auto
   ```

#### Step 1.3: Configure Cloudflare SSL
1. **Go to SSL/TLS → Overview**
2. **Set SSL mode to**: "Flexible" or "Full"
3. **Enable**: "Always Use HTTPS"
4. **Go to SSL/TLS → Edge Certificates**
5. **Enable**: "Always Use HTTPS"

### Phase 2: Update Backend Configuration

#### Step 2.1: SSH into Backend Server (***********)
```bash
# Connect to your backend server
ssh root@***********

# Navigate to project directory
cd /var/www/streamdb_onl_usr/data/www/streamdb.online
```

#### Step 2.2: Update Environment Variables
```bash
# Backup current .env file
cp server/.env server/.env.backup.$(date +%Y%m%d-%H%M%S)

# Edit the .env file
nano server/.env
```

**Update these lines in server/.env:**
```env
# Change from:
FRONTEND_URL=https://streamdb.online
CORS_ORIGIN=https://streamdb.online,http://localhost:5173
VITE_API_BASE_URL=https://streamdb.online/api
ADMIN_EMAIL=<EMAIL>

# Change to (replace newdomain.com with your actual new domain):
FRONTEND_URL=https://newdomain.com
CORS_ORIGIN=https://newdomain.com,http://localhost:5173
VITE_API_BASE_URL=https://newdomain.com/api
ADMIN_EMAIL=<EMAIL>
```

#### Step 2.3: Update Frontend Environment
```bash
# Edit frontend .env file
nano .env
```

**Update these lines in .env:**
```env
# Change from:
VITE_API_BASE_URL=https://streamdb.online/api

# Change to:
VITE_API_BASE_URL=https://newdomain.com/api
```

#### Step 2.4: Rebuild Frontend
```bash
# Install dependencies and rebuild
npm install
npm run build

# Restart Node.js application
pm2 restart streamdb-online
pm2 save
```

### Phase 3: Update Reverse Proxy Configuration

#### Step 3.1: SSH into Reverse Proxy Server (*************)
```bash
# Connect to your reverse proxy server
ssh root@*************
```

#### Step 3.2: Update Nginx Configuration
```bash
# Backup current nginx config
cp /etc/nginx/sites-available/streamdb.online /etc/nginx/sites-available/streamdb.online.backup.$(date +%Y%m%d-%H%M%S)

# Create new config for new domain
nano /etc/nginx/sites-available/newdomain.com
```

**Copy this configuration (replace newdomain.com with your actual domain):**
```nginx
# WebSocket upgrade map
map $http_upgrade $connection_upgrade {
    default upgrade;
    '' close;
}

# Rate limiting zones
limit_req_zone $binary_remote_addr zone=general:10m rate=10r/s;
limit_req_zone $binary_remote_addr zone=api:10m rate=5r/s;
limit_req_zone $binary_remote_addr zone=admin:10m rate=20r/s;

# Main website - HTTP to HTTPS redirect
server {
    listen 80;
    server_name newdomain.com www.newdomain.com;
    
    # Security headers even for redirects
    add_header X-Frame-Options "DENY" always;
    add_header X-Content-Type-Options "nosniff" always;
    
    # Redirect all HTTP to HTTPS
    return 301 https://$server_name$request_uri;
}

# Main website - HTTPS
server {
    listen 443 ssl http2;
    server_name newdomain.com www.newdomain.com;
    
    # SSL Configuration (Cloudflare handles certificates)
    ssl_certificate /etc/ssl/certs/cloudflare-origin.pem;
    ssl_certificate_key /etc/ssl/private/cloudflare-origin.key;
    
    # SSL Security Settings
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    
    # Security Headers
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains; preload" always;
    add_header X-Frame-Options "DENY" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    
    # API routes with rate limiting
    location /api/ {
        limit_req zone=api burst=20 nodelay;
        
        proxy_pass http://***********:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection $connection_upgrade;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $host;
        proxy_set_header X-Forwarded-Port $server_port;
        proxy_cache_bypass $http_upgrade;
        
        # API timeouts
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
    
    # Admin routes with stricter rate limiting
    location /admin {
        limit_req zone=admin burst=10 nodelay;
        
        proxy_pass http://***********:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_cache_bypass $http_upgrade;
        
        # No cache for admin pages
        expires -1;
        add_header Cache-Control "no-cache, no-store, must-revalidate";
    }
    
    # Static files and main application
    location / {
        proxy_pass http://***********:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_cache_bypass $http_upgrade;
        
        # Cache static assets
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
            proxy_pass http://***********:3001;
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }
    
    # Health check endpoint
    location /health {
        proxy_pass http://***********:3001/api/health;
        access_log off;
    }
    
    # Block access to sensitive files
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }
    
    location ~ /(\.env|\.git|node_modules|server|database) {
        deny all;
        access_log off;
        log_not_found off;
    }
    
    # Logging
    access_log /var/log/nginx/newdomain_access.log;
    error_log /var/log/nginx/newdomain_error.log;
}

# FastPanel subdomain - HTTP to HTTPS redirect
server {
    listen 80;
    server_name fastpanel.newdomain.com;
    
    return 301 https://$server_name$request_uri;
}

# FastPanel subdomain - HTTPS
server {
    listen 443 ssl http2;
    server_name fastpanel.newdomain.com;
    
    # SSL Configuration (same as main site)
    ssl_certificate /etc/ssl/certs/cloudflare-origin.pem;
    ssl_certificate_key /etc/ssl/private/cloudflare-origin.key;
    
    # SSL Security Settings
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    
    # Security Headers
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains; preload" always;
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    
    # Proxy to FastPanel on backend server
    location / {
        proxy_pass http://***********:8888;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection $connection_upgrade;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $host;
        proxy_set_header X-Forwarded-Port $server_port;
        proxy_set_header X-Scheme $scheme;
        proxy_cache_bypass $http_upgrade;
        
        # Disable proxy buffering for real-time responses
        proxy_buffering off;
        proxy_request_buffering off;
        
        # Increase timeouts for file operations
        proxy_connect_timeout 300s;
        proxy_send_timeout 300s;
        proxy_read_timeout 300s;
        send_timeout 300s;
    }
    
    # Logging
    access_log /var/log/nginx/fastpanel_newdomain_access.log;
    error_log /var/log/nginx/fastpanel_newdomain_error.log;
}
```

#### Step 3.3: Enable New Configuration
```bash
# Enable the new site
ln -s /etc/nginx/sites-available/newdomain.com /etc/nginx/sites-enabled/

# Test nginx configuration
nginx -t

# If test passes, reload nginx
systemctl reload nginx
```

### Phase 4: Test New Domain

#### Step 4.1: Wait for DNS Propagation
```bash
# Wait 5-30 minutes for DNS to propagate
# Check DNS resolution
nslookup newdomain.com
dig newdomain.com
```

#### Step 4.2: Test Website Access
```bash
# Test main website
curl -I https://newdomain.com/

# Test API endpoint
curl -I https://newdomain.com/api/health

# Test admin panel
curl -I https://newdomain.com/admin

# Test FastPanel
curl -I https://fastpanel.newdomain.com/
```

#### Step 4.3: Browser Testing
1. **Open browser and visit**: `https://newdomain.com`
2. **Check all functionality**:
   - Homepage loads correctly
   - Movies and series pages work
   - Search functionality works
   - Admin panel accessible
   - FastPanel accessible at `https://fastpanel.newdomain.com`

---

## 🔄 SCENARIO 2: MULTI-DOMAIN SETUP WITH 301 REDIRECTS

### Phase 1: Setup Additional Domains

#### Step 1.1: Add Multiple Domains to Cloudflare
**Repeat for each additional domain (domain2.com, domain3.com, etc.):**

1. **Add each domain to Cloudflare**
2. **Configure DNS records** (same as Phase 1 above)
3. **Set SSL mode to "Flexible" or "Full"**

#### Step 1.2: Update Backend CORS Settings
```bash
# SSH into backend server
ssh root@***********

# Edit .env file
nano /var/www/streamdb_onl_usr/data/www/streamdb.online/server/.env
```

**Update CORS_ORIGIN to include all domains:**
```env
# Add all your domains separated by commas
CORS_ORIGIN=https://newdomain.com,https://domain2.com,https://domain3.com,http://localhost:5173
```

### Phase 2: Configure Multi-Domain Nginx

#### Step 2.1: Create Master Configuration
```bash
# SSH into reverse proxy server
ssh root@*************

# Create multi-domain configuration
nano /etc/nginx/sites-available/streamdb-multi-domain.conf
```

**Multi-Domain Configuration with 301 Redirects:**
```nginx
# Define primary domain (your main domain)
set $primary_domain "newdomain.com";

# WebSocket upgrade map
map $http_upgrade $connection_upgrade {
    default upgrade;
    '' close;
}

# Rate limiting zones
limit_req_zone $binary_remote_addr zone=general:10m rate=10r/s;
limit_req_zone $binary_remote_addr zone=api:10m rate=5r/s;
limit_req_zone $binary_remote_addr zone=admin:10m rate=20r/s;

# HTTP to HTTPS redirect for all domains
server {
    listen 80;
    server_name newdomain.com www.newdomain.com domain2.com www.domain2.com domain3.com www.domain3.com;

    # Security headers even for redirects
    add_header X-Frame-Options "DENY" always;
    add_header X-Content-Type-Options "nosniff" always;

    # Redirect all HTTP to HTTPS
    return 301 https://$server_name$request_uri;
}

# Primary domain - Full functionality
server {
    listen 443 ssl http2;
    server_name newdomain.com www.newdomain.com;

    # SSL Configuration
    ssl_certificate /etc/ssl/certs/cloudflare-origin.pem;
    ssl_certificate_key /etc/ssl/private/cloudflare-origin.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;

    # Security Headers
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains; preload" always;
    add_header X-Frame-Options "DENY" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;

    # API routes
    location /api/ {
        limit_req zone=api burst=20 nodelay;
        proxy_pass http://***********:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection $connection_upgrade;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $host;
        proxy_set_header X-Forwarded-Port $server_port;
        proxy_cache_bypass $http_upgrade;
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }

    # Admin routes
    location /admin {
        limit_req zone=admin burst=10 nodelay;
        proxy_pass http://***********:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_cache_bypass $http_upgrade;
        expires -1;
        add_header Cache-Control "no-cache, no-store, must-revalidate";
    }

    # Main application
    location / {
        proxy_pass http://***********:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_cache_bypass $http_upgrade;

        # Cache static assets
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
            proxy_pass http://***********:3001;
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }

    # Health check
    location /health {
        proxy_pass http://***********:3001/api/health;
        access_log off;
    }

    # Block sensitive files
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }

    location ~ /(\.env|\.git|node_modules|server|database) {
        deny all;
        access_log off;
        log_not_found off;
    }

    # Logging
    access_log /var/log/nginx/primary_domain_access.log;
    error_log /var/log/nginx/primary_domain_error.log;
}

# Secondary domains - 301 redirect to primary domain
server {
    listen 443 ssl http2;
    server_name domain2.com www.domain2.com domain3.com www.domain3.com;

    # SSL Configuration (same certificates work for all domains)
    ssl_certificate /etc/ssl/certs/cloudflare-origin.pem;
    ssl_certificate_key /etc/ssl/private/cloudflare-origin.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;

    # 301 redirect to primary domain
    return 301 https://newdomain.com$request_uri;
}

# FastPanel for primary domain
server {
    listen 80;
    server_name fastpanel.newdomain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name fastpanel.newdomain.com;

    ssl_certificate /etc/ssl/certs/cloudflare-origin.pem;
    ssl_certificate_key /etc/ssl/private/cloudflare-origin.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;

    # Security Headers
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains; preload" always;
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;

    location / {
        proxy_pass http://***********:8888;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection $connection_upgrade;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $host;
        proxy_set_header X-Forwarded-Port $server_port;
        proxy_set_header X-Scheme $scheme;
        proxy_cache_bypass $http_upgrade;
        proxy_buffering off;
        proxy_request_buffering off;
        proxy_connect_timeout 300s;
        proxy_send_timeout 300s;
        proxy_read_timeout 300s;
        send_timeout 300s;
    }

    access_log /var/log/nginx/fastpanel_access.log;
    error_log /var/log/nginx/fastpanel_error.log;
}
```

#### Step 2.2: Enable Multi-Domain Configuration
```bash
# Disable old configurations
rm -f /etc/nginx/sites-enabled/streamdb.online
rm -f /etc/nginx/sites-enabled/newdomain.com

# Enable multi-domain configuration
ln -s /etc/nginx/sites-available/streamdb-multi-domain.conf /etc/nginx/sites-enabled/

# Test configuration
nginx -t

# Reload nginx
systemctl reload nginx
```

### Phase 3: Alternative Multi-Domain Strategy (Load Balancing)

#### Step 3.1: Round-Robin Load Balancing Setup
```bash
# Create load-balanced configuration
nano /etc/nginx/sites-available/streamdb-load-balanced.conf
```

**Load Balanced Configuration:**
```nginx
# Define upstream servers (all pointing to same backend but different domains)
upstream streamdb_backend {
    # All requests go to same backend server
    server ***********:3001 weight=1;

    # Health check settings
    keepalive 32;
    keepalive_requests 100;
    keepalive_timeout 60s;
}

# WebSocket upgrade map
map $http_upgrade $connection_upgrade {
    default upgrade;
    '' close;
}

# Rate limiting zones
limit_req_zone $binary_remote_addr zone=general:10m rate=10r/s;
limit_req_zone $binary_remote_addr zone=api:10m rate=5r/s;
limit_req_zone $binary_remote_addr zone=admin:10m rate=20r/s;

# HTTP to HTTPS redirect for all domains
server {
    listen 80;
    server_name newdomain.com www.newdomain.com domain2.com www.domain2.com domain3.com www.domain3.com;

    add_header X-Frame-Options "DENY" always;
    add_header X-Content-Type-Options "nosniff" always;

    return 301 https://$server_name$request_uri;
}

# All domains serve the same content (no redirects)
server {
    listen 443 ssl http2;
    server_name newdomain.com www.newdomain.com domain2.com www.domain2.com domain3.com www.domain3.com;

    # SSL Configuration
    ssl_certificate /etc/ssl/certs/cloudflare-origin.pem;
    ssl_certificate_key /etc/ssl/private/cloudflare-origin.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;

    # Security Headers
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains; preload" always;
    add_header X-Frame-Options "DENY" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;

    # API routes
    location /api/ {
        limit_req zone=api burst=20 nodelay;
        proxy_pass http://streamdb_backend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection $connection_upgrade;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $host;
        proxy_set_header X-Forwarded-Port $server_port;
        proxy_cache_bypass $http_upgrade;
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }

    # Admin routes
    location /admin {
        limit_req zone=admin burst=10 nodelay;
        proxy_pass http://streamdb_backend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_cache_bypass $http_upgrade;
        expires -1;
        add_header Cache-Control "no-cache, no-store, must-revalidate";
    }

    # Main application
    location / {
        proxy_pass http://streamdb_backend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_cache_bypass $http_upgrade;

        # Cache static assets
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
            proxy_pass http://streamdb_backend;
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }

    # Health check
    location /health {
        proxy_pass http://streamdb_backend/api/health;
        access_log off;
    }

    # Block sensitive files
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }

    location ~ /(\.env|\.git|node_modules|server|database) {
        deny all;
        access_log off;
        log_not_found off;
    }

    # Logging with domain identification
    access_log /var/log/nginx/multi_domain_access.log combined;
    error_log /var/log/nginx/multi_domain_error.log;
}
```

---

## 🔧 TROUBLESHOOTING GUIDE

### Common Issues and Solutions

#### Issue 1: DNS Not Resolving
**Symptoms**: Domain doesn't load, DNS errors
**Solutions**:
```bash
# Check DNS propagation
nslookup newdomain.com
dig newdomain.com

# Clear DNS cache
# Windows:
ipconfig /flushdns

# Linux:
sudo systemctl restart systemd-resolved

# Wait 30 minutes for global DNS propagation
```

#### Issue 2: SSL Certificate Errors
**Symptoms**: "Your connection is not private" errors
**Solutions**:
1. **Check Cloudflare SSL mode**:
   - Go to Cloudflare → SSL/TLS → Overview
   - Set to "Flexible" or "Full"
   - Enable "Always Use HTTPS"

2. **Verify SSL certificates on proxy server**:
```bash
# Check if certificates exist
ls -la /etc/ssl/certs/cloudflare-origin.pem
ls -la /etc/ssl/private/cloudflare-origin.key

# If missing, download from Cloudflare:
# Go to Cloudflare → SSL/TLS → Origin Certificates
# Create new certificate and download
```

#### Issue 3: 502 Bad Gateway Errors
**Symptoms**: Nginx shows 502 errors
**Solutions**:
```bash
# Check backend server status
ssh root@***********

# Check if Node.js is running
pm2 list
pm2 logs streamdb-online

# Restart if needed
pm2 restart streamdb-online

# Check if port 3001 is listening
netstat -tlnp | grep :3001
```

#### Issue 4: CORS Errors
**Symptoms**: API calls fail with CORS errors
**Solutions**:
```bash
# Update CORS settings in backend
ssh root@***********
nano /var/www/streamdb_onl_usr/data/www/streamdb.online/server/.env

# Add all domains to CORS_ORIGIN:
CORS_ORIGIN=https://newdomain.com,https://domain2.com,https://domain3.com

# Restart backend
pm2 restart streamdb-online
```

#### Issue 5: FastPanel Not Accessible
**Symptoms**: FastPanel subdomain doesn't work
**Solutions**:
```bash
# Check FastPanel2 status on backend server
ssh root@***********
systemctl status fastpanel2

# Check if port 8888 is listening
netstat -tlnp | grep :8888

# Restart FastPanel2 if needed
systemctl restart fastpanel2
```

---

## 📋 VERIFICATION CHECKLIST

### After Domain Migration:
- [ ] New domain resolves to correct IP (*************)
- [ ] HTTPS works without certificate errors
- [ ] Main website loads correctly
- [ ] All pages and functionality work
- [ ] API endpoints respond correctly
- [ ] Admin panel is accessible
- [ ] FastPanel subdomain works
- [ ] Search functionality works
- [ ] Movie/series pages load
- [ ] Mobile responsiveness maintained

### After Multi-Domain Setup:
- [ ] All domains resolve correctly
- [ ] Primary domain serves full content
- [ ] Secondary domains redirect properly (if using 301 method)
- [ ] OR all domains serve content (if using load balancing method)
- [ ] No CORS errors on any domain
- [ ] FastPanel accessible on primary domain
- [ ] All functionality works on all domains

---

## 🚨 EMERGENCY ROLLBACK PROCEDURE

### If New Domain Fails:

#### Step 1: Restore Original Configuration
```bash
# On reverse proxy server (*************)
ssh root@*************

# Disable new configuration
rm -f /etc/nginx/sites-enabled/newdomain.com
rm -f /etc/nginx/sites-enabled/streamdb-multi-domain.conf

# Re-enable original configuration
ln -s /etc/nginx/sites-available/streamdb.online /etc/nginx/sites-enabled/

# Test and reload
nginx -t
systemctl reload nginx
```

#### Step 2: Restore Backend Settings
```bash
# On backend server (***********)
ssh root@***********

# Restore original .env file
cp /var/www/streamdb_onl_usr/data/www/streamdb.online/server/.env.backup.* /var/www/streamdb_onl_usr/data/www/streamdb.online/server/.env

# Restart backend
pm2 restart streamdb-online
```

#### Step 3: Verify Original Domain Works
```bash
# Test original domain
curl -I https://streamdb.online/
curl -I https://streamdb.online/api/health
```

---

## 📞 SUPPORT COMMANDS

### Check System Status:
```bash
# Check all services on backend server
ssh root@***********
systemctl status nginx
systemctl status mysql
systemctl status fastpanel2
pm2 list

# Check reverse proxy server
ssh root@*************
systemctl status nginx
nginx -t
```

### Monitor Logs:
```bash
# Backend server logs
tail -f /var/log/nginx/streamdb_backend_access.log
tail -f /var/log/nginx/streamdb_backend_error.log
pm2 logs streamdb-online

# Reverse proxy logs
tail -f /var/log/nginx/streamdb_access.log
tail -f /var/log/nginx/streamdb_error.log
```

### Test Connectivity:
```bash
# Test from reverse proxy to backend
curl -I http://***********:3001/api/health

# Test external access
curl -I https://newdomain.com/
curl -I https://newdomain.com/api/health
```

---

## 🎯 FINAL NOTES

### Best Practices:
1. **Always backup configurations** before making changes
2. **Test in staging environment** if possible
3. **Monitor logs** during and after migration
4. **Keep old domain active** for 30 days during transition
5. **Update all external links** and bookmarks
6. **Notify users** about domain change if applicable

### Security Considerations:
1. **Never expose backend server IP** directly
2. **Always use HTTPS** for all domains
3. **Keep Cloudflare proxy enabled** (orange cloud)
4. **Regularly update SSL certificates**
5. **Monitor for unauthorized access attempts**

### Performance Optimization:
1. **Use Cloudflare caching** for static assets
2. **Enable gzip compression** in nginx
3. **Set appropriate cache headers**
4. **Monitor server resources** during high traffic
5. **Consider CDN** for global content delivery

---

**Document Version**: 1.0
**Last Updated**: 2025-01-21
**For**: StreamDB Domain Migration & Multi-Domain Setup
