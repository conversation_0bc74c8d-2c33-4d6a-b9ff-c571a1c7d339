# Backend Server Configuration Documentation

## Server Information
## Server Details
- **Server IP:** ***********
- **Hostname:** backend1maindb
- **OS:** Ubuntu 24.04.2 LTS
- **Kernel:** 6.8.0-63-generic
- **Architecture:** x86_64
- **Documentation Generated:** Mon Jul 21 15:01:34 UTC 2025

## System Resources
### CPU Information
```
Architecture:                         x86_64
CPU op-mode(s):                       32-bit, 64-bit
Address sizes:                        40 bits physical, 48 bits virtual
Byte Order:                           Little Endian
CPU(s):                               2
On-line CPU(s) list:                  0,1
Vendor ID:                            GenuineIntel
BIOS Vendor ID:                       QEMU
Model name:                           QEMU Virtual CPU version 2.5+
BIOS Model name:                      pc-i440fx-9.0  CPU @ 2.0GHz
BIOS CPU family:                      1
CPU family:                           15
Model:                                107
Thread(s) per core:                   1
Core(s) per socket:                   2
Socket(s):                            1
Stepping:                             1
BogoMIPS:                             4195.13
Flags:                                fpu de pse tsc msr pae mce cx8 apic sep mtrr pge mca cmov pat pse36 clflush mmx fxsr sse sse2 ht syscall nx lm constant_tsc nopl xtopology cpuid tsc_known_freq pni ssse3 cx16 sse4_1 sse4_2 x2apic popcnt aes hypervisor lahf_lm cpuid_fault pti
Hypervisor vendor:                    KVM
Virtualization type:                  full
L1d cache:                            64 KiB (2 instances)
L1i cache:                            64 KiB (2 instances)
L2 cache:                             8 MiB (2 instances)
L3 cache:                             16 MiB (1 instance)
NUMA node(s):                         1
NUMA node0 CPU(s):                    0,1
Vulnerability Gather data sampling:   Not affected
Vulnerability Itlb multihit:          KVM: Mitigation: VMX unsupported
Vulnerability L1tf:                   Mitigation; PTE Inversion
Vulnerability Mds:                    Vulnerable: Clear CPU buffers attempted, no microcode; SMT Host state unknown
Vulnerability Meltdown:               Mitigation; PTI
Vulnerability Mmio stale data:        Unknown: No mitigations
Vulnerability Reg file data sampling: Not affected
Vulnerability Retbleed:               Not affected
Vulnerability Spec rstack overflow:   Not affected
Vulnerability Spec store bypass:      Vulnerable
Vulnerability Spectre v1:             Mitigation; usercopy/swapgs barriers and __user pointer sanitization
Vulnerability Spectre v2:             Mitigation; Retpolines; STIBP disabled; RSB filling; PBRSB-eIBRS Not affected; BHI Retpoline
Vulnerability Srbds:                  Not affected
Vulnerability Tsx async abort:        Not affected
```

### Memory Information
```
               total        used        free      shared  buff/cache   available
Mem:           3.8Gi       1.0Gi       613Mi       7.3Mi       2.5Gi       2.8Gi
Swap:             0B          0B          0B
```

### Disk Usage
```
Filesystem      Size  Used Avail Use% Mounted on
tmpfs           392M  1.1M  391M   1% /run
/dev/sda1        38G  6.0G   32G  16% /
tmpfs           2.0G  1.2M  2.0G   1% /dev/shm
tmpfs           5.0M     0  5.0M   0% /run/lock
/dev/sda16      881M  113M  707M  14% /boot
/dev/sda15      105M  6.2M   99M   6% /boot/efi
tmpfs           392M   12K  392M   1% /run/user/0
```

## Network Configuration
### Network Interfaces
```
1: lo: <LOOPBACK,UP,LOWER_UP> mtu 65536 qdisc noqueue state UNKNOWN group default qlen 1000
    link/loopback 00:00:00:00:00:00 brd 00:00:00:00:00:00
    inet 127.0.0.1/8 scope host lo
       valid_lft forever preferred_lft forever
    inet6 ::1/128 scope host noprefixroute 
       valid_lft forever preferred_lft forever
2: eth0: <BROADCAST,MULTICAST,UP,LOWER_UP> mtu 1500 qdisc fq_codel state UP group default qlen 1000
    link/ether 64:39:0f:4d:5a:39 brd ff:ff:ff:ff:ff:ff
    altname enp0s18
    inet ***********/24 brd *********** scope global eth0
       valid_lft forever preferred_lft forever
    inet6 fe80::6639:fff:fe4d:5a39/64 scope link 
       valid_lft forever preferred_lft forever
```

### Routing Table
```
default via ********* dev eth0 proto static 
*********/24 dev eth0 proto kernel scope link src *********** 
```

## FastPanel Control Panel
### FastPanel Service Status
```
FastPanel service not found via systemctl
```

### FastPanel Configuration Files
## Web Server Configuration
### Apache Configuration
#### Apache Version
```
```

#### Apache Service Status
```
```

#### Apache Main Configuration
##### Configuration: /etc/apache2/apache2.conf
```apache
# this file was autogenerated, please do not edit

ServerName localhost

Mutex file:${APACHE_LOCK_DIR} default

PidFile ${APACHE_PID_FILE}

Timeout 300

KeepAlive On

MaxKeepAliveRequests 100

KeepAliveTimeout 5

# These need to be set in /etc/apache2/envvars
User ${APACHE_RUN_USER}
Group ${APACHE_RUN_GROUP}

HostnameLookups Off

ErrorLog ${APACHE_LOG_DIR}/error.log

LogLevel warn

# Include module configuration:
IncludeOptional mods-enabled/*.load
IncludeOptional mods-enabled/*.conf

# Include list of ports to listen on
Include ports.conf

<Directory />
Options FollowSymLinks
AllowOverride None
Require all denied
</Directory>

<Directory /usr/share>
AllowOverride None
Require all granted
</Directory>

<Directory /var/www/>
Options Indexes FollowSymLinks
AllowOverride None
Require all granted
</Directory>

AccessFileName .htaccess

<FilesMatch "^\.ht">
Require all denied
</FilesMatch>

LogFormat "%v:%p %h %l %u %t \"%r\" %>s %O \"%{Referer}i\" \"%{User-Agent}i\"" vhost_combined
LogFormat "%a %l %u %t \"%r\" %>s %O \"%{Referer}i\" \"%{User-Agent}i\"" combined
LogFormat "%h %l %u %t \"%r\" %>s %O" common
LogFormat "%{Referer}i -> %U" referer
LogFormat "%{User-agent}i" agent

# Include generic snippets of statements
IncludeOptional conf-enabled/*.conf

# Include the virtual host configurations:
IncludeOptional sites-enabled/*.conf
IncludeOptional fastpanel2-sites/*/*.conf
# Include generic snippets of statements
IncludeOptional conf.d/
```

#### Apache Virtual Hosts
### NGINX Configuration
#### NGINX Version
```
```

#### NGINX Service Status
```
● nginx.service - nginx - high performance web server
     Loaded: loaded (/usr/lib/systemd/system/nginx.service; enabled; preset: enabled)
     Active: active (running) since Wed 2025-07-16 06:34:51 UTC; 5 days ago
       Docs: https://nginx.org/en/docs/
   Main PID: 506146 (nginx)
      Tasks: 3 (limit: 4655)
     Memory: 5.0M (peak: 7.4M)
        CPU: 29.428s
     CGroup: /system.slice/nginx.service
             ├─506146 "nginx: master process /usr/sbin/nginx -c /etc/nginx/nginx.conf"
             ├─554068 "nginx: worker process"
             └─554069 "nginx: worker process"

Jul 17 05:30:35 backend1maindb systemd[1]: Reloading nginx.service - nginx - high performance web server...
Jul 17 05:30:35 backend1maindb systemd[1]: Reloaded nginx.service - nginx - high performance web server.
Jul 18 00:15:20 backend1maindb systemd[1]: Reloading nginx.service - nginx - high performance web server...
Jul 18 00:15:20 backend1maindb systemd[1]: Reloaded nginx.service - nginx - high performance web server.
```

## MySQL Database Configuration
### MySQL Service Status
```
```

### MySQL Version
```
```

### MySQL Configuration
#### MySQL Config: /etc/mysql/my.cnf
```ini
#
# The MySQL database server configuration file.
#
# You can copy this to one of:
# - "/etc/mysql/my.cnf" to set global options,
# - "~/.my.cnf" to set user-specific options.
# 
# One can use all long options that the program supports.
# Run program with --help to get a list of available options and with
# --print-defaults to see which it would actually understand and use.
#
# For explanations see
# http://dev.mysql.com/doc/mysql/en/server-system-variables.html

#
# * IMPORTANT: Additional settings that can override those from this file!
#   The files must end with '.cnf', otherwise they'll be ignored.
#

!includedir /etc/mysql/conf.d/
!includedir /etc/mysql/mysql.conf.d/
!include /etc/mysql/my.cnf.fastpanel/99-fastpanel.cnf
```

#### MySQL Config Directory: /etc/mysql/conf.d
##### Config: mysql.cnf
```ini
[mysql]
```

##### Config: mysqldump.cnf
```ini
[mysqldump]
quick
quote-names
max_allowed_packet	= 16M
```

#### MySQL Config Directory: /etc/mysql/mysql.conf.d
##### Config: mysql.cnf
```ini
#
# The MySQL database client configuration file
#
# Ref to https://dev.mysql.com/doc/refman/en/mysql-command-options.html

[mysql]
```

##### Config: mysqld.cnf
```ini
#
# The MySQL database server configuration file.
# Optimized for StreamDB Production Environment
#
# For explanations see
# http://dev.mysql.com/doc/mysql/en/server-system-variables.html

# Here is entries for some specific programs
# The following values assume you have at least 32M ram

[mysqld]
#
# * Basic Settings
#
user            = mysql
pid-file        = /var/run/mysqld/mysqld.pid
socket          = /var/run/mysqld/mysqld.sock
port            = 3306
datadir         = /var/lib/mysql

# Security and Network Settings
bind-address            = 127.0.0.1
mysqlx-bind-address     = 127.0.0.1
skip-networking         = false
skip-name-resolve       = true

# Character Set and Collation
character-set-server    = utf8mb4
collation-server        = utf8mb4_unicode_ci

#
# * Fine Tuning for Production
#
key_buffer_size         = 32M
max_allowed_packet      = 64M
thread_stack            = 256K
thread_cache_size       = 8

# Connection Settings
max_connections         = 100
connect_timeout         = 10
wait_timeout            = 600
interactive_timeout     = 600

# InnoDB Settings for Performance
innodb_buffer_pool_size = 256M
innodb_log_file_size    = 64M
innodb_log_buffer_size  = 16M
innodb_flush_log_at_trx_commit = 1
innodb_lock_wait_timeout = 50

# MyISAM Settings
myisam-recover-options  = BACKUP

# Table Settings
table_open_cache        = 2000
table_definition_cache  = 1400

# Query Cache (disabled in MySQL 8.0+)
# query_cache_type       = 1
# query_cache_size       = 32M

# Temporary Tables
tmp_table_size          = 32M
max_heap_table_size     = 32M

# Slow Query Log
slow_query_log          = 1
slow_query_log_file     = /var/log/mysql/mysql-slow.log
long_query_time         = 2
log-queries-not-using-indexes = 1

#
# * Logging and Replication
#
# Error log - should be very few entries.
log_error = /var/log/mysql/error.log

# Binary Logging
max_binlog_size   = 100M
binlog_expire_logs_seconds = 2592000

# General Log (disabled for performance)
# general_log_file        = /var/log/mysql/query.log
# general_log             = 1

#
# * Security Settings
#
# Disable LOAD DATA LOCAL INFILE for security
local-infile = 0

# SSL Settings (optional)
# ssl-ca = /etc/mysql/ssl/ca-cert.pem
# ssl-cert = /etc/mysql/ssl/server-cert.pem
# ssl-key = /etc/mysql/ssl/server-key.pem

[mysql]
default-character-set = utf8mb4

[client]
default-character-set = utf8mb4
```

## PHP Configuration
### PHP Version
```
```

### PHP Configuration
```
```

## Website Configuration
### Document Root Locations
```
/var/www/streamdb_root/data/www/streamdb.online
```

## SSL Certificates
### SSL Certificate Locations
```
/etc/ssl/private/ssl-cert-snakeoil.key
/etc/ssl/certs/Certum_Trusted_Network_CA_2.pem
/etc/ssl/certs/GlobalSign_Root_CA_-_R3.pem
/etc/ssl/certs/DigiCert_Global_Root_G2.pem
/etc/ssl/certs/QuoVadis_Root_CA_2_G3.pem
/etc/ssl/certs/Telia_Root_CA_v2.pem
/etc/ssl/certs/certSIGN_Root_CA_G2.pem
/etc/ssl/certs/Izenpe.com.pem
/etc/ssl/certs/AffirmTrust_Commercial.pem
/etc/ssl/certs/SwissSign_Silver_CA_-_G2.pem
/etc/ssl/certs/AffirmTrust_Networking.pem
/etc/ssl/certs/GlobalSign_Root_CA.pem
/etc/ssl/certs/SZAFIR_ROOT_CA2.pem
/etc/ssl/certs/Certainly_Root_R1.pem
/etc/ssl/certs/Entrust_Root_Certification_Authority_-_EC1.pem
/etc/ssl/certs/Security_Communication_ECC_RootCA1.pem
/etc/ssl/certs/Entrust_Root_Certification_Authority_-_G4.pem
/etc/ssl/certs/DigiCert_TLS_RSA4096_Root_G5.pem
/etc/ssl/certs/Trustwave_Global_ECC_P384_Certification_Authority.pem
/etc/ssl/certs/USERTrust_ECC_Certification_Authority.pem
/etc/ssl/certs/BJCA_Global_Root_CA1.pem
/etc/ssl/certs/Go_Daddy_Class_2_CA.pem
/etc/ssl/certs/D-TRUST_EV_Root_CA_1_2020.pem
/etc/ssl/certs/OISTE_WISeKey_Global_Root_GB_CA.pem
/etc/ssl/certs/Sectigo_Public_Server_Authentication_Root_E46.pem
/etc/ssl/certs/Comodo_AAA_Services_root.pem
/etc/ssl/certs/GLOBALTRUST_2020.pem
/etc/ssl/certs/DigiCert_Assured_ID_Root_G3.pem
/etc/ssl/certs/SSL.com_TLS_ECC_Root_CA_2022.pem
/etc/ssl/certs/CommScope_Public_Trust_RSA_Root-01.pem
/etc/ssl/certs/GlobalSign_ECC_Root_CA_-_R5.pem
/etc/ssl/certs/GlobalSign_Root_E46.pem
/etc/ssl/certs/Starfield_Root_Certificate_Authority_-_G2.pem
/etc/ssl/certs/T-TeleSec_GlobalRoot_Class_3.pem
/etc/ssl/certs/NetLock_Arany_=Class_Gold=_Főtanúsítvány.pem
/etc/ssl/certs/Secure_Global_CA.pem
/etc/ssl/certs/IdenTrust_Public_Sector_Root_CA_1.pem
/etc/ssl/certs/ISRG_Root_X2.pem
/etc/ssl/certs/NAVER_Global_Root_Certification_Authority.pem
/etc/ssl/certs/Hellenic_Academic_and_Research_Institutions_RootCA_2015.pem
/etc/ssl/certs/SSL.com_TLS_RSA_Root_CA_2022.pem
/etc/ssl/certs/Entrust_Root_Certification_Authority.pem
/etc/ssl/certs/Atos_TrustedRoot_Root_CA_RSA_TLS_2021.pem
/etc/ssl/certs/Sectigo_Public_Server_Authentication_Root_R46.pem
/etc/ssl/certs/UCA_Global_G2_Root.pem
/etc/ssl/certs/emSign_ECC_Root_CA_-_G3.pem
/etc/ssl/certs/DigiCert_Assured_ID_Root_CA.pem
/etc/ssl/certs/ANF_Secure_Server_Root_CA.pem
/etc/ssl/certs/Security_Communication_RootCA3.pem
/etc/ssl/certs/AC_RAIZ_FNMT-RCM.pem
/etc/ssl/certs/DigiCert_Trusted_Root_G4.pem
/etc/ssl/certs/certSIGN_ROOT_CA.pem
/etc/ssl/certs/CommScope_Public_Trust_ECC_Root-01.pem
/etc/ssl/certs/SSL.com_EV_Root_Certification_Authority_ECC.pem
/etc/ssl/certs/TunTrust_Root_CA.pem
/etc/ssl/certs/Buypass_Class_3_Root_CA.pem
/etc/ssl/certs/Hellenic_Academic_and_Research_Institutions_ECC_RootCA_2015.pem
/etc/ssl/certs/SwissSign_Gold_CA_-_G2.pem
/etc/ssl/certs/Starfield_Services_Root_Certificate_Authority_-_G2.pem
/etc/ssl/certs/Atos_TrustedRoot_2011.pem
/etc/ssl/certs/SSL.com_EV_Root_Certification_Authority_RSA_R2.pem
/etc/ssl/certs/Baltimore_CyberTrust_Root.pem
/etc/ssl/certs/Amazon_Root_CA_3.pem
/etc/ssl/certs/Trustwave_Global_ECC_P256_Certification_Authority.pem
/etc/ssl/certs/DigiCert_Assured_ID_Root_G2.pem
/etc/ssl/certs/AffirmTrust_Premium_ECC.pem
/etc/ssl/certs/AC_RAIZ_FNMT-RCM_SERVIDORES_SEGUROS.pem
/etc/ssl/certs/QuoVadis_Root_CA_1_G3.pem
/etc/ssl/certs/GTS_Root_R2.pem
/etc/ssl/certs/Amazon_Root_CA_4.pem
/etc/ssl/certs/Security_Communication_Root_CA.pem
/etc/ssl/certs/CA_Disig_Root_R2.pem
/etc/ssl/certs/GlobalSign_Root_CA_-_R6.pem
/etc/ssl/certs/D-TRUST_BR_Root_CA_1_2020.pem
/etc/ssl/certs/ACCVRAIZ1.pem
/etc/ssl/certs/Certainly_Root_E1.pem
/etc/ssl/certs/CommScope_Public_Trust_ECC_Root-02.pem
/etc/ssl/certs/COMODO_ECC_Certification_Authority.pem
/etc/ssl/certs/Security_Communication_RootCA2.pem
/etc/ssl/certs/Hongkong_Post_Root_CA_3.pem
/etc/ssl/certs/Microsoft_RSA_Root_Certificate_Authority_2017.pem
/etc/ssl/certs/GlobalSign_Root_R46.pem
/etc/ssl/certs/D-TRUST_Root_Class_3_CA_2_2009.pem
/etc/ssl/certs/DigiCert_High_Assurance_EV_Root_CA.pem
/etc/ssl/certs/TeliaSonera_Root_CA_v1.pem
/etc/ssl/certs/QuoVadis_Root_CA_3.pem
/etc/ssl/certs/HARICA_TLS_ECC_Root_CA_2021.pem
/etc/ssl/certs/ca-certificates.crt
/etc/ssl/certs/BJCA_Global_Root_CA2.pem
/etc/ssl/certs/DigiCert_TLS_ECC_P384_Root_G5.pem
/etc/ssl/certs/vTrus_ECC_Root_CA.pem
/etc/ssl/certs/HARICA_TLS_RSA_Root_CA_2021.pem
/etc/ssl/certs/SSL.com_Root_Certification_Authority_RSA.pem
/etc/ssl/certs/GlobalSign_ECC_Root_CA_-_R4.pem
/etc/ssl/certs/Certum_EC-384_CA.pem
/etc/ssl/certs/TWCA_Root_Certification_Authority.pem
/etc/ssl/certs/COMODO_Certification_Authority.pem
/etc/ssl/certs/ssl-cert-snakeoil.pem
/etc/ssl/certs/QuoVadis_Root_CA_3_G3.pem
/etc/ssl/certs/emSign_Root_CA_-_G1.pem
/etc/ssl/certs/TUBITAK_Kamu_SM_SSL_Kok_Sertifikasi_-_Surum_1.pem
/etc/ssl/certs/Certum_Trusted_Network_CA.pem
/etc/ssl/certs/GTS_Root_R1.pem
/etc/ssl/certs/DigiCert_Global_Root_G3.pem
/etc/ssl/certs/Atos_TrustedRoot_Root_CA_ECC_TLS_2021.pem
/etc/ssl/certs/QuoVadis_Root_CA_2.pem
/etc/ssl/certs/USERTrust_RSA_Certification_Authority.pem
/etc/ssl/certs/ISRG_Root_X1.pem
/etc/ssl/certs/IdenTrust_Commercial_Root_CA_1.pem
/etc/ssl/certs/Entrust_Root_Certification_Authority_-_G2.pem
/etc/ssl/certs/vTrus_Root_CA.pem
/etc/ssl/certs/Autoridad_de_Certificacion_Firmaprofesional_CIF_A62634068.pem
/etc/ssl/certs/T-TeleSec_GlobalRoot_Class_2.pem
/etc/ssl/certs/Starfield_Class_2_CA.pem
/etc/ssl/certs/Microsec_e-Szigno_Root_CA_2009.pem
/etc/ssl/certs/COMODO_RSA_Certification_Authority.pem
/etc/ssl/certs/Go_Daddy_Root_Certificate_Authority_-_G2.pem
/etc/ssl/certs/emSign_Root_CA_-_C1.pem
/etc/ssl/certs/GDCA_TrustAUTH_R5_ROOT.pem
/etc/ssl/certs/OISTE_WISeKey_Global_Root_GC_CA.pem
/etc/ssl/certs/GTS_Root_R3.pem
/etc/ssl/certs/Actalis_Authentication_Root_CA.pem
/etc/ssl/certs/XRamp_Global_CA_Root.pem
/etc/ssl/certs/ePKI_Root_Certification_Authority.pem
/etc/ssl/certs/SecureSign_RootCA11.pem
/etc/ssl/certs/TWCA_Global_Root_CA.pem
/etc/ssl/certs/TrustAsia_Global_Root_CA_G4.pem
/etc/ssl/certs/HiPKI_Root_CA_-_G1.pem
/etc/ssl/certs/AffirmTrust_Premium.pem
/etc/ssl/certs/CommScope_Public_Trust_RSA_Root-02.pem
/etc/ssl/certs/Amazon_Root_CA_1.pem
/etc/ssl/certs/SSL.com_Root_Certification_Authority_ECC.pem
/etc/ssl/certs/Certum_Trusted_Root_CA.pem
/etc/ssl/certs/Buypass_Class_2_Root_CA.pem
/etc/ssl/certs/emSign_ECC_Root_CA_-_C3.pem
/etc/ssl/certs/Certigna.pem
/etc/ssl/certs/UCA_Extended_Validation_Root.pem
/etc/ssl/certs/SecureTrust_CA.pem
/etc/ssl/certs/Certigna_Root_CA.pem
/etc/ssl/certs/GTS_Root_R4.pem
/etc/ssl/certs/Microsoft_ECC_Root_Certificate_Authority_2017.pem
/etc/ssl/certs/CFCA_EV_ROOT.pem
/etc/ssl/certs/DigiCert_Global_Root_CA.pem
/etc/ssl/certs/TrustAsia_Global_Root_CA_G3.pem
/etc/ssl/certs/Trustwave_Global_Certification_Authority.pem
/etc/ssl/certs/Entrust.net_Premium_2048_Secure_Server_CA.pem
/etc/ssl/certs/Amazon_Root_CA_2.pem
/etc/ssl/certs/e-Szigno_Root_CA_2017.pem
/etc/ssl/certs/D-TRUST_Root_Class_3_CA_2_EV_2009.pem
```

## Running Services
### Active Services
```
  UNIT                                           LOAD   ACTIVE SUB     DESCRIPTION
  apache2.service                                loaded active running The Apache HTTP Server
  apparmor.service                               loaded active exited  Load AppArmor profiles
  apport.service                                 loaded active exited  automatic crash report generation
  blk-availability.service                       loaded active exited  Availability of block devices
  cloud-config.service                           loaded active exited  Cloud-init: Config Stage
  cloud-final.service                            loaded active exited  Cloud-init: Final Stage
  cloud-init-local.service                       loaded active exited  Cloud-init: Local Stage (pre-network)
  cloud-init.service                             loaded active exited  Cloud-init: Network Stage
  console-setup.service                          loaded active exited  Set console font and keymap
  cron.service                                   loaded active running Regular background program processing daemon
  dbus.service                                   loaded active running D-Bus System Message Bus
  dovecot.service                                loaded active running Dovecot IMAP/POP3 email server
  exim4.service                                  loaded active running exim Mail Transport Agent
  fail2ban.service                               loaded active running Fail2Ban Service
  fastpanel2-apps.service                        loaded active running FASTPANEL applications pool
  fastpanel2-nginx.service                       loaded active running FASTPANEL web server
  fastpanel2.service                             loaded active running FASTPANEL
  faststat.service                               loaded active running faststat for FASTPANEL
  finalrd.service                                loaded active exited  Create final runtime dir for shutdown pivot root
  <EMAIL>                             loaded active running Getty on tty1
  keyboard-setup.service                         loaded active exited  Set the console keyboard layout
  kmod-static-nodes.service                      loaded active exited  Create List of Static Device Nodes
  lvm2-monitor.service                           loaded active exited  Monitoring of LVM2 mirrors, snapshots etc. using dmeventd or progress polling
  ModemManager.service                           loaded active running Modem Manager
  multipathd.service                             loaded active running Device-Mapper Multipath Device Controller
  mysql.service                                  loaded active running MySQL Community Server
  nginx.service                                  loaded active running nginx - high performance web server
  php8.3-fpm.service                             loaded active running The PHP 8.3 FastCGI Process Manager
  plymouth-quit-wait.service                     loaded active exited  Hold until boot process finishes up
  plymouth-quit.service                          loaded active exited  Terminate Plymouth Boot Screen
  plymouth-read-write.service                    loaded active exited  Tell Plymouth To Write Out Runtime Data
  polkit.service                                 loaded active running Authorization Manager
  proftpd.service                                loaded active running ProFTPD FTP Server
  rsyslog.service                                loaded active running System Logging Service
  <EMAIL>                     loaded active running Serial Getty on ttyS0
  setvtrgb.service                               loaded active exited  Set console scheme
  snapd.apparmor.service                         loaded active exited  Load AppArmor profiles managed internally by snapd
  snapd.seeded.service                           loaded active exited  Wait until snapd is fully seeded
  ssh.service                                    loaded active running OpenBSD Secure Shell server
  sysstat.service                                loaded active exited  Resets System Activity Logs
  systemd-binfmt.service                         loaded active exited  Set Up Additional Binary Formats
  systemd-fsck@dev-disk-by\x2dlabel-BOOT.service loaded active exited  File System Check on /dev/disk/by-label/BOOT
  systemd-fsck@dev-disk-by\x2dlabel-UEFI.service loaded active exited  File System Check on /dev/disk/by-label/UEFI
  systemd-journal-flush.service                  loaded active exited  Flush Journal to Persistent Storage
  systemd-journald.service                       loaded active running Journal Service
  systemd-logind.service                         loaded active running User Login Management
  systemd-modules-load.service                   loaded active exited  Load Kernel Modules
  systemd-networkd-wait-online.service           loaded active exited  Wait for Network to be Configured
  systemd-networkd.service                       loaded active running Network Configuration
  systemd-random-seed.service                    loaded active exited  Load/Save OS Random Seed
  systemd-remount-fs.service                     loaded active exited  Remount Root and Kernel File Systems
  systemd-resolved.service                       loaded active running Network Name Resolution
  systemd-sysctl.service                         loaded active exited  Apply Kernel Variables
  systemd-timesyncd.service                      loaded active running Network Time Synchronization
  systemd-tmpfiles-setup-dev-early.service       loaded active exited  Create Static Device Nodes in /dev gracefully
  systemd-tmpfiles-setup-dev.service             loaded active exited  Create Static Device Nodes in /dev
  systemd-tmpfiles-setup.service                 loaded active exited  Create Volatile Files and Directories
  systemd-udev-trigger.service                   loaded active exited  Coldplug All udev Devices
  systemd-udevd.service                          loaded active running Rule-based Manager for Device Events and Files
  systemd-update-utmp.service                    loaded active exited  Record System Boot/Shutdown in UTMP
  systemd-user-sessions.service                  loaded active exited  Permit User Sessions
  udisks2.service                                loaded active running Disk Manager
  ufw.service                                    loaded active exited  Uncomplicated firewall
  unattended-upgrades.service                    loaded active running Unattended Upgrades Shutdown
  <EMAIL>                     loaded active exited  User Runtime Directory /run/user/0
  <EMAIL>                                 loaded active running User Manager for UID 0

Legend: LOAD   → Reflects whether the unit definition was properly loaded.
        ACTIVE → The high-level unit activation state, i.e. generalization of SUB.
        SUB    → The low-level unit activation state, values depend on unit type.

66 loaded units listed.
```

### Open Ports
```
Active Internet connections (only servers)
Proto Recv-Q Send-Q Local Address           Foreign Address         State       PID/Program name    
tcp        0      0 0.0.0.0:110             0.0.0.0:*               LISTEN      506244/dovecot      
tcp        0      0 ***********:443         0.0.0.0:*               LISTEN      506146/nginx: maste 
tcp        0      0 0.0.0.0:25              0.0.0.0:*               LISTEN      507606/exim4        
tcp        0      0 0.0.0.0:143             0.0.0.0:*               LISTEN      506244/dovecot      
tcp        0      0 127.0.0.1:5501          0.0.0.0:*               LISTEN      553961/fastpanel    
tcp        0      0 127.0.0.1:3306          0.0.0.0:*               LISTEN      506512/mysqld       
tcp        0      0 0.0.0.0:465             0.0.0.0:*               LISTEN      507606/exim4        
tcp        0      0 ***********:80          0.0.0.0:*               LISTEN      506146/nginx: maste 
tcp        0      0 0.0.0.0:587             0.0.0.0:*               LISTEN      507606/exim4        
tcp        0      0 0.0.0.0:8888            0.0.0.0:*               LISTEN      506145/nginx: maste 
tcp        0      0 0.0.0.0:995             0.0.0.0:*               LISTEN      506244/dovecot      
tcp        0      0 0.0.0.0:993             0.0.0.0:*               LISTEN      506244/dovecot      
tcp        0      0 127.0.0.1:81            0.0.0.0:*               LISTEN      577551/apache2      
tcp        0      0 0.0.0.0:7777            0.0.0.0:*               LISTEN      506145/nginx: maste 
tcp        0      0 **********:53           0.0.0.0:*               LISTEN      506138/systemd-reso 
tcp        0      0 127.0.0.54:53           0.0.0.0:*               LISTEN      506138/systemd-reso 
tcp6       0      0 :::110                  :::*                    LISTEN      506244/dovecot      
tcp6       0      0 :::22                   :::*                    LISTEN      1/systemd           
tcp6       0      0 :::21                   :::*                    LISTEN      621385/proftpd: (ac 
tcp6       0      0 :::25                   :::*                    LISTEN      507606/exim4        
tcp6       0      0 :::143                  :::*                    LISTEN      506244/dovecot      
tcp6       0      0 :::465                  :::*                    LISTEN      507606/exim4        
tcp6       0      0 :::587                  :::*                    LISTEN      507606/exim4        
tcp6       0      0 :::995                  :::*                    LISTEN      506244/dovecot      
tcp6       0      0 :::993                  :::*                    LISTEN      506244/dovecot      
tcp6       0      0 :::3001                 :::*                    LISTEN      657889/node /var/ww 
udp        0      0 127.0.0.54:53           0.0.0.0:*                           506138/systemd-reso 
udp        0      0 **********:53           0.0.0.0:*                           506138/systemd-reso 
udp        0      0 ***********:443         0.0.0.0:*                           506146/nginx: maste 
udp        0      0 ***********:443         0.0.0.0:*                           506146/nginx: maste 
```

## Firewall Configuration
### UFW Status
```
Status: active
Logging: on (low)
Default: deny (incoming), allow (outgoing), disabled (routed)
New profiles: skip

To                         Action      From
--                         ------      ----
22/tcp                     ALLOW IN    Anywhere                  
80/tcp                     ALLOW IN    Anywhere                  
443/tcp                    ALLOW IN    Anywhere                  
8888/tcp                   ALLOW IN    Anywhere                  
Anywhere                   ALLOW IN    *************             
22/tcp (v6)                ALLOW IN    Anywhere (v6)             
80/tcp (v6)                ALLOW IN    Anywhere (v6)             
443/tcp (v6)               ALLOW IN    Anywhere (v6)             
8888/tcp (v6)              ALLOW IN    Anywhere (v6)             

```

