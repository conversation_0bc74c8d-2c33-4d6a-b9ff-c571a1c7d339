
import * as React from "react";
import apiService from "@/services/apiService";
import { MediaItem } from "@/types/media";
import { Search as SearchIcon } from "lucide-react";
import { Link, useLocation } from "react-router-dom";
import { scrollToTop } from "@/utils/scrollToTop";

export default function SearchBar() {
  const location = useLocation();
  const [query, setQuery] = React.useState("");
  const [results, setResults] = React.useState<MediaItem[]>([]);
  const [isSearching, setIsSearching] = React.useState(false);
  const [error, setError] = React.useState<string | null>(null);
  const inputRef = React.useRef<HTMLInputElement>(null);
  const containerRef = React.useRef<HTMLDivElement>(null);
  const searchTimeoutRef = React.useRef<NodeJS.Timeout | null>(null);

  // Debounced search function
  const performSearch = React.useCallback(async (searchQuery: string) => {
    if (searchQuery.trim().length === 0) {
      setResults([]);
      setError(null);
      return;
    }

    if (searchQuery.trim().length < 2) {
      return; // Don't search for single characters
    }

    setIsSearching(true);
    setError(null);

    try {
      const response = await apiService.searchContent(searchQuery, {
        published: true,
        limit: 5,
        sort_by: 'created_at',
        sort_order: 'desc'
      });

      if (response && response.success && Array.isArray(response.data)) {
        setResults(response.data);
      } else {
        setResults([]);
        setError('No results found');
      }
    } catch (error) {
      console.error('Search error:', error);
      setResults([]);
      setError('Search failed. Please try again.');
    } finally {
      setIsSearching(false);
    }
  }, []);

  React.useEffect(() => {
    // Clear previous timeout
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }

    // Set new timeout for debounced search
    searchTimeoutRef.current = setTimeout(() => {
      performSearch(query);
    }, 300); // 300ms debounce

    // Cleanup timeout on unmount
    return () => {
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }
    };
  }, [query, performSearch]);

  // Close search results when clicking outside
  React.useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (containerRef.current && !containerRef.current.contains(event.target as Node)) {
        setResults([]);
        setQuery("");
        setError(null);
        setIsSearching(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  return (
    <div className="relative" ref={containerRef}>
      <div className="flex items-center bg-[#181B1D] border border-border rounded-xl px-3 py-1.5 shadow focus-within:ring-2 focus-within:ring-primary transition-all gap-1.5">
        <input
          type="text"
          className="flex-1 bg-transparent outline-none text-xs sm:text-sm text-foreground placeholder:text-muted-foreground font-medium"
          placeholder="Search movies, series & content..."
          value={query}
          onChange={(e) => setQuery(e.target.value)}
          ref={inputRef}
          aria-label="Search"
        />
        <button
          type="button"
          className="ml-1 text-muted-foreground hover:text-primary focus:outline-none"
          tabIndex={-1}
        >
          <SearchIcon size={18} />
        </button>
      </div>
      {results.length > 0 && (
        <div className="absolute left-0 top-10 w-full bg-card rounded-lg shadow-lg border border-border mt-2 z-50 animate-fade-in">
          <ul>
            {results.map((item) => (
              <li key={item.id}>
                <Link
                  to={`/content/${item.id}`}
                  state={{ from: location.pathname }}
                  className="flex items-center gap-2 px-3 py-1.5 hover:bg-muted/80 transition rounded"
                  onClick={() => {
                    setQuery("");
                    scrollToTop();
                  }}
                >
                  <img
                    src={item.image}
                    alt={item.title}
                    className="w-7 h-9 rounded object-cover"
                  />
                  <span className="font-medium text-sm truncate">{item.title}</span>
                  <span className="text-xs ml-2 text-muted-foreground">
                    {item.type === "movie" ? "Movie" : "Web Series"}
                  </span>
                </Link>
              </li>
            ))}
          </ul>
        </div>
      )}
    </div>
  );
}

