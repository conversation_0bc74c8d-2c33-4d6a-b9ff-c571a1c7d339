
import * as React from "react";
import { Link, useLocation } from "react-router-dom";
import { MediaItem } from "@/types/media";
import { MediaItemExtended, normalizeMediaItem } from '@/types/mediaItemExtended';
import { ChevronLeft, ChevronRight, Play } from "lucide-react";
import { getHomepageContent } from "@/utils/contentFilters";
import { scrollToTop } from "@/utils/scrollToTop";

interface HeroCarouselProps {
  contentData?: MediaItem[];
}

// Get carousel content using the content filter utility with safety checks
const getCarouselData = (contentData: MediaItemExtended[] = []) => {
  try {
    // Normalize all items to handle both camelCase and snake_case fields
    const normalizedContent = contentData.map(item => ({
      ...item,
      // Ensure addToCarousel is set if add_to_carousel exists
      addToCarousel: item.addToCarousel !== undefined ? item.addToCarousel : !!item.add_to_carousel,
      // Ensure carouselPosition is set if carousel_position exists
      carouselPosition: item.carouselPosition || item.carousel_position
    }));
    
    // Get carousel content with proper sorting by position
    const { carousel: carouselContent } = getHomepageContent(normalizedContent, 20);

    // Ensure we have valid content and enforce the limit of exactly 10 items
    const validCarouselContent = Array.isArray(carouselContent)
      ? carouselContent
          .filter(item => item && item.id && item.title)
          // Double-check sorting by position
          .sort((a, b) => {
            const posA = a.carouselPosition || a.carousel_position || 999;
            const posB = b.carouselPosition || b.carousel_position || 999;
            return posA - posB;
          })
          .slice(0, 10) // Ensure exactly 10 items maximum
      : [];

    // Fallback to first 5 items if no carousel content is available
    const fallbackContent = Array.isArray(contentData)
      ? contentData.slice(0, 5).filter(item => item && item.id && item.title)
      : [];

    const result = validCarouselContent.length > 0 ? validCarouselContent : fallbackContent;
    return result;
  } catch (error) {
    console.error('Error getting carousel data:', error);
    return [];
  }
};

export default function HeroCarousel({ contentData = [] }: HeroCarouselProps) {
  const location = useLocation();
  const featured = getCarouselData(contentData);
  const [idx, setIdx] = React.useState(0);
  const [showFullDescription, setShowFullDescription] = React.useState(false);
  const [windowWidth, setWindowWidth] = React.useState(typeof window !== 'undefined' ? window.innerWidth : 1024);
  const [isMobile, setIsMobile] = React.useState(typeof window !== 'undefined' ? window.innerWidth <= 768 : false);

  React.useEffect(() => {
    if (featured.length === 0) return;

    // Auto-advance carousel every 8 seconds (8000ms) - increased for better UX
    const t = setInterval(() => {
      setIdx((v) => (v + 1) % featured.length);
      setShowFullDescription(false); // Reset description on auto-advance
    }, 8000);
    
    return () => clearInterval(t);
  }, [featured.length]);

  // Handle window resize for responsive description truncation and mobile detection
  React.useEffect(() => {
    let resizeTimeout: NodeJS.Timeout;
    
    const handleResize = () => {
      // Debounce resize handler to prevent excessive re-renders
      clearTimeout(resizeTimeout);
      resizeTimeout = setTimeout(() => {
        const newWidth = window.innerWidth;
        setWindowWidth(newWidth);
        setIsMobile(newWidth <= 768);
        setShowFullDescription(false); // Reset description on resize
      }, 150); // 150ms debounce
    };

    window.addEventListener('resize', handleResize);
    return () => {
      window.removeEventListener('resize', handleResize);
      clearTimeout(resizeTimeout);
    };
  }, []);

  // Safety checks for featured array and current item
  if (!featured || featured.length === 0) {
    return (
      <section className="relative h-[280px] sm:h-[350px] md:h-[420px] lg:h-[490px] flex items-center justify-center w-full mb-6 sm:mb-8 rounded-lg overflow-hidden shadow animate-fade-in bg-muted">
        <div className="text-center text-muted-foreground">
          <div className="text-4xl mb-2">🎬</div>
          <p>Loading content...</p>
        </div>
      </section>
    );
  }

  const item = featured[idx];

  // Null safety check for current item
  if (!item || !item.id) {
    return (
      <section className="relative h-[280px] sm:h-[350px] md:h-[420px] lg:h-[490px] flex items-center justify-center w-full mb-6 sm:mb-8 rounded-lg overflow-hidden shadow animate-fade-in bg-muted">
        <div className="text-center text-muted-foreground">
          <div className="text-4xl mb-2">🎬</div>
          <p>Loading content...</p>
        </div>
      </section>
    );
  }

  // Enhanced genre processing with debugging
  let processedGenres = [];
  if (item.genres) {
    if (Array.isArray(item.genres)) {
      processedGenres = item.genres.filter(g => g && typeof g === 'string' && g.trim().length > 0);
    } else if (typeof item.genres === 'string') {
      // Handle comma-separated string genres
      processedGenres = item.genres.split(',').map(g => g.trim()).filter(g => g.length > 0);
    }
  }

  // Remove debug logging to improve performance

  // Enhanced image URL selection with HD quality preference
  const getOptimalImageUrl = (item: any) => {
    // Priority order: backdrop (for hero carousel) > poster > fallback
    const backdropUrl = item.backdropUrl || item.backdrop_url;
    const posterUrl = item.posterUrl || item.poster_url || item.coverImage || item.image;
    
    // For TMDB images, ensure we're using high quality versions
    if (backdropUrl) {
      // Replace size in TMDB URL to get original quality
      const hdBackdrop = backdropUrl.replace(/\/w\d+/, '/original');
      return hdBackdrop;
    }
    
    if (posterUrl) {
      // For poster images, use w1280 for HD quality (good balance of quality and loading speed)
      const hdPoster = posterUrl.replace(/\/w\d+/, '/w1280');
      return hdPoster;
    }
    
    return '/placeholder-image.jpg';
  };

  // Apply crop settings to image style - simplified and reliable approach
  const getImageStyle = (item: any) => {
    const baseStyle: React.CSSProperties = {
      objectFit: 'cover',
      objectPosition: 'center center',
      imageRendering: 'high-quality'
    };

    // Apply crop settings if available
    if (item.crop_settings) {
      const { x = 50, y = 50, width = 100, height = 100, scale = 1 } = item.crop_settings;
      
      // Simple and reliable approach using only objectPosition and transform
      return {
        ...baseStyle,
        objectFit: 'cover',
        objectPosition: `${x}% ${y}%`,
        transform: scale !== 1 ? `scale(${scale})` : 'none',
        transformOrigin: `${x}% ${y}%`
      };
    }

    return baseStyle;
  };

  // Create safe item object with HD image URLs
  const safeItem = {
    id: item.id,
    title: item.title || 'Untitled',
    description: item.description || 'No description available',
    coverImage: getOptimalImageUrl(item),
    image: getOptimalImageUrl(item),
    genres: processedGenres,
    year: item.year || 'Unknown',
    crop_settings: item.crop_settings // Pass through crop settings
  };

  // Mobile dynamic font sizing - NO ELLIPSIS, guaranteed single-line fit
  const getDynamicTitleFontSize = (title: string) => {
    const length = title.length;
    // Enhanced mobile sizing to eliminate ellipsis - smaller sizes for guaranteed fit
    if (length <= 10) return "clamp(1.25rem, 5vw, 2rem)"; // Very short titles
    if (length <= 15) return "clamp(1rem, 4vw, 1.5rem)"; // Short titles
    if (length <= 20) return "clamp(0.875rem, 3.5vw, 1.25rem)"; // Medium titles
    if (length <= 25) return "clamp(0.75rem, 3vw, 1rem)"; // Long titles
    if (length <= 30) return "clamp(0.65rem, 2.5vw, 0.875rem)"; // Very long titles
    if (length <= 35) return "clamp(0.6rem, 2.25vw, 0.75rem)"; // Extra long titles
    if (length <= 40) return "clamp(0.55rem, 2vw, 0.65rem)"; // Extremely long titles
    return "clamp(0.5rem, 1.75vw, 0.6rem)"; // Maximum length titles - smallest but readable
  };

  // Desktop/Tablet dynamic Tailwind classes - RESTORED ORIGINAL BEHAVIOR
  const getTitleFontSize = (title: string) => {
    const length = title.length;
    // Original dynamic Tailwind classes based on title length
    if (length <= 15) return 'text-3xl sm:text-4xl md:text-5xl lg:text-6xl xl:text-7xl'; // Short titles - largest
    if (length <= 25) return 'text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl'; // Medium titles
    if (length <= 35) return 'text-xl sm:text-2xl md:text-3xl lg:text-4xl xl:text-5xl'; // Long titles
    if (length <= 45) return 'text-lg sm:text-xl md:text-2xl lg:text-3xl xl:text-4xl'; // Very long titles
    return 'text-base sm:text-lg md:text-xl lg:text-2xl xl:text-3xl'; // Extra long titles - smallest
  };

  // Description truncation and expansion logic with device-specific limits
  const getDescriptionDisplay = (description: string) => {
    // Different character limits for different screen sizes - more conservative for mobile
    const getMaxLength = () => {
      if (windowWidth < 480) return 35; // Very small mobile - very short
      if (windowWidth < 640) return 45; // Mobile - short limit for guaranteed single line
      if (windowWidth < 768) return 80; // Small tablet
      if (windowWidth < 1024) return 110; // Tablet
      return 140; // Desktop - longer limit
    };

    const maxLength = getMaxLength();
    
    if (description.length <= maxLength) {
      return { text: description, needsMore: false };
    }
    
    if (showFullDescription) {
      return { text: description, needsMore: true };
    }
    
    const truncated = description.substring(0, maxLength).trim();
    const lastSpace = truncated.lastIndexOf(' ');
    const finalText = lastSpace > 0 ? truncated.substring(0, lastSpace) : truncated;
    
    return { text: finalText + '...', needsMore: true };
  };

  function handlePrev(e?: React.MouseEvent) {
    // Prevent bubbling focus to underlying layers
    e?.stopPropagation?.();
    setIdx((idx - 1 + featured.length) % featured.length);
    setShowFullDescription(false); // Reset description state on slide change
  }
  function handleNext(e?: React.MouseEvent) {
    e?.stopPropagation?.();
    setIdx((idx + 1) % featured.length);
    setShowFullDescription(false); // Reset description state on slide change
  }

  return (
    <section className="relative w-full mb-4 sm:mb-6 rounded-lg overflow-hidden shadow animate-fade-in group">
      {/* Responsive container with proper aspect ratios and contained overflow */}
      <div className="relative w-full aspect-[16/9] sm:aspect-[21/9] md:aspect-[2.5/1] lg:aspect-[3/1] overflow-hidden bg-gray-900">
        {/* HD Background image with responsive sizing and proper fitting */}
        <div className="absolute inset-0 overflow-hidden">
          <picture className="absolute inset-0">
          {/* High-res source for larger screens */}
          <source 
            media="(min-width: 1024px)" 
            srcSet={`${safeItem.coverImage.replace(/\/w\d+/, '/w1920')} 1920w, ${safeItem.coverImage.replace(/\/w\d+/, '/original')} 2000w`}
            sizes="100vw"
          />
          {/* Medium-res source for tablets */}
          <source 
            media="(min-width: 768px)" 
            srcSet={`${safeItem.coverImage.replace(/\/w\d+/, '/w1280')} 1280w`}
            sizes="100vw"
          />
          {/* Lower-res source for mobile */}
          <source 
            media="(max-width: 767px)" 
            srcSet={`${safeItem.coverImage.replace(/\/w\d+/, '/w780')} 780w`}
            sizes="100vw"
          />
          {/* Fallback image with enhanced object-fit and crop settings */}
          <img
            src={safeItem.coverImage}
            alt={safeItem.title}
            className="absolute inset-0 w-full h-full brightness-75 transition-all duration-700 pointer-events-none select-none group-hover:brightness-50"
            style={getImageStyle(safeItem)}
            draggable={false}
            loading="eager"
            onError={(e) => {
              e.currentTarget.src = '/placeholder-image.jpg';
            }}
          />
          </picture>
        </div>
        
        {/* Enhanced gradient overlay for better text readability */}
        <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/20 to-transparent pointer-events-none select-none" />
        <div className="absolute inset-0 bg-gradient-to-r from-black/50 via-transparent to-transparent pointer-events-none select-none" />

        {/* Clickable overlay for navigation */}
        <Link
          to={`/content/${safeItem.id}`}
          state={{ from: location.pathname }}
          onClick={scrollToTop}
          className="absolute inset-0 z-5 cursor-pointer"
          aria-label={`View ${safeItem.title}`}
        />
        
        {/* Carousel controls - positioned within the responsive container */}
        <button
          className="absolute left-2 sm:left-3 md:left-4 top-1/2 -translate-y-1/2 bg-black/60 backdrop-blur-sm text-white p-2 sm:p-3 rounded-full z-20 hover:bg-black/80 hover:scale-110 transition-all duration-200 shadow-lg border border-white/20 min-h-[44px] min-w-[44px] flex items-center justify-center"
          onClick={handlePrev}
          aria-label="Previous"
          tabIndex={0}
          style={{ outline: "none" }}
        >
          <ChevronLeft size={20} className="sm:w-6 sm:h-6" />
        </button>
        <button
          className="absolute right-2 sm:right-3 md:right-4 top-1/2 -translate-y-1/2 bg-black/60 backdrop-blur-sm text-white p-2 sm:p-3 rounded-full z-20 hover:bg-black/80 hover:scale-110 transition-all duration-200 shadow-lg border border-white/20 min-h-[44px] min-w-[44px] flex items-center justify-center"
          onClick={handleNext}
          aria-label="Next"
          tabIndex={0}
          style={{ outline: "none" }}
        >
          <ChevronRight size={20} className="sm:w-6 sm:h-6" />
        </button>

        {/* Main content - positioned to avoid left button overlap */}
        <div className="absolute inset-0 z-10 flex flex-col justify-center pointer-events-none select-none">
          {/* Content container with proper mobile positioning - Fixed Issue #2 */}
          <div className="ml-16 sm:ml-20 md:ml-24 lg:ml-28 xl:ml-32 mr-8 sm:mr-12 md:mr-16 lg:mr-20 xl:mr-24 max-w-none sm:max-w-2xl md:max-w-3xl lg:max-w-4xl hero-carousel-content"
               style={{
                 // Mobile-specific positioning: move content 15% to the right from current position
                 marginLeft: isMobile ? "clamp(3rem, 6vw, 4rem)" : "clamp(4rem, 8vw, 8rem)",
                 marginRight: isMobile ? "clamp(2rem, 4vw, 3rem)" : "clamp(4rem, 8vw, 8rem)",
                 maxWidth: isMobile ? "calc(100vw - 5rem)" : "calc(100vw - 8rem)"
               }}>
            
            {/* Content Type Badge - Fixed Issue #1: Proper theme colors */}
            <span className="inline-block mb-2 sm:mb-3 px-2 sm:px-3 py-1 rounded-full text-xs font-semibold uppercase tracking-widest pointer-events-auto select-auto backdrop-blur-sm"
                  style={{
                    backgroundColor: "hsl(var(--primary))",
                    color: "hsl(var(--primary-foreground))",
                    boxShadow: "0 2px 8px rgba(0, 0, 0, 0.3)"
                  }}>
              {(item && item.type === "movie") ? "Movie" : "Web Series"}
            </span>
            
            {/* Dynamic Title - DUAL DYNAMIC SIZING: Mobile inline CSS + Desktop Tailwind classes */}
            <h2
              className={`font-extrabold mb-2 sm:mb-3 drop-shadow-2xl ${!isMobile ? getTitleFontSize(safeItem.title) : ''}`}
              style={{
                color: "#fff",
                textTransform: "uppercase",
                fontFamily: "'Koulen', Impact, Arial, sans-serif",
                letterSpacing: "0.06em",
                fontWeight: 400,
                lineHeight: 1.1,
                textShadow: "2px 2px 4px rgba(0,0,0,0.8)",
                // Mobile: Dynamic inline CSS sizing (NO ELLIPSIS)
                // Desktop/Tablet: Use Tailwind classes (restored original behavior)
                fontSize: isMobile ? getDynamicTitleFontSize(safeItem.title) : undefined,
                // Mobile: NO text overflow handling to prevent ellipsis
                // Desktop/Tablet: Allow natural text flow with Tailwind responsive classes
                whiteSpace: "nowrap",
                overflow: isMobile ? "visible" : "hidden",
                textOverflow: isMobile ? "unset" : "ellipsis",
                maxWidth: "100%",
                // Ensure consistent rendering across browsers
                wordWrap: "normal",
                wordBreak: "normal",
                // Enhanced cross-browser compatibility
                WebkitFontSmoothing: "antialiased",
                MozOsxFontSmoothing: "grayscale",
                textRendering: "optimizeLegibility"
              }}
              title={safeItem.title}
            >
              {safeItem.title}
            </h2>
            
            {/* Description with expand/collapse functionality */}
            <div className="mb-2 sm:mb-3 text-white drop-shadow-lg pointer-events-auto select-auto">
              {showFullDescription ? (
                // Expanded description - can wrap to multiple lines
                <div 
                  className="leading-relaxed text-sm sm:text-base"
                  style={{
                    textShadow: "1px 1px 2px rgba(0,0,0,0.8)",
                    maxWidth: "calc(100vw - 8rem)",
                    wordWrap: "break-word",
                    overflowWrap: "break-word"
                  }}
                >
                  {getDescriptionDisplay(safeItem.description).text}
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      setShowFullDescription(false);
                    }}
                    className="ml-2 text-primary hover:text-primary/80 font-medium underline transition-colors duration-200"
                    style={{ 
                      textShadow: "1px 1px 2px rgba(0,0,0,0.8)",
                      fontSize: "inherit"
                    }}
                  >
                    less
                  </button>
                </div>
              ) : (
                // Truncated description - absolutely single line only
                <div 
                  className="w-full"
                  style={{
                    maxWidth: "calc(100vw - 8rem)",
                    height: "1.2em", // Fixed height to prevent line breaks
                    overflow: "hidden"
                  }}
                >
                  <div 
                    className="flex items-center h-full"
                    style={{
                      whiteSpace: "nowrap",
                      overflow: "hidden"
                    }}
                  >
                    <span 
                      className="text-sm sm:text-base"
                      style={{
                        textShadow: "1px 1px 2px rgba(0,0,0,0.8)",
                        whiteSpace: "nowrap",
                        overflow: "hidden",
                        textOverflow: "ellipsis",
                        maxWidth: "calc(100% - 3rem)", // Reserve space for "more" button
                        display: "inline-block"
                      }}
                    >
                      {getDescriptionDisplay(safeItem.description).text}
                    </span>
                    {getDescriptionDisplay(safeItem.description).needsMore && (
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          setShowFullDescription(true);
                        }}
                        className="ml-2 text-primary hover:text-primary/80 font-medium underline transition-colors duration-200 text-sm sm:text-base"
                        style={{ 
                          textShadow: "1px 1px 2px rgba(0,0,0,0.8)",
                          whiteSpace: "nowrap",
                          flexShrink: 0
                        }}
                      >
                        more
                      </button>
                    )}
                  </div>
                </div>
              )}
            </div>
            
            {/* Genres with proper spacing - Fixed Issue #1: Proper theme colors */}
            <div className="flex gap-1 sm:gap-2 flex-wrap pt-1 mb-3 sm:mb-4 pointer-events-auto select-auto">
              {safeItem.genres.slice(0, 4).map(g => (
                <span
                  key={g}
                  className="px-2 sm:px-3 py-1 rounded text-xs font-bold uppercase tracking-wide shadow-lg backdrop-blur-sm transition-transform duration-200 hover:scale-105"
                  style={{
                    backgroundColor: "hsl(var(--primary))",
                    color: "hsl(var(--primary-foreground))",
                    fontSize: "0.75em",
                    letterSpacing: "0.04em",
                    boxShadow: "0 2px 8px rgba(0, 0, 0, 0.3)"
                  }}
                >
                  {g}
                </span>
              ))}
              <span className="text-xs inline-flex items-center text-white ml-2 sm:ml-3 font-medium" style={{ textShadow: "1px 1px 2px rgba(0,0,0,0.8)" }}>
                {safeItem.year}
              </span>
            </div>

            {/* Watch Now Button - Fixed Issue #1: Proper theme colors */}
            <div className="pointer-events-auto">
              <Link
                to={`/content/${safeItem.id}`}
                state={{ from: location.pathname }}
                onClick={scrollToTop}
                className="inline-flex items-center gap-2 px-4 sm:px-6 py-2 sm:py-3 rounded-lg font-semibold transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105 backdrop-blur-sm"
                style={{
                  backgroundColor: "hsl(var(--primary))",
                  color: "hsl(var(--primary-foreground))",
                  boxShadow: "0 4px 12px rgba(0, 0, 0, 0.3)"
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.backgroundColor = "hsl(var(--primary) / 0.9)";
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.backgroundColor = "hsl(var(--primary))";
                }}
              >
                <Play className="w-4 h-4 sm:w-5 sm:h-5" />
                <span className="text-sm sm:text-base">Watch Now</span>
              </Link>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}

