#!/bin/bash

# ============================================================================
# StreamDB Enhanced Reverse Proxy Server Automated Maintenance Script
# Server: backend2ndrevproxy (*************)
# Purpose: Comprehensive weekly maintenance with advanced monitoring
# Schedule: Every Thursday at 00:30 (30 minutes after backend)
# Version: 2.0 Enhanced
# ============================================================================

set -euo pipefail  # Exit on error, undefined vars, pipe failures

# Configuration
SCRIPT_NAME="StreamDB Enhanced Reverse Proxy Maintenance"
SCRIPT_VERSION="2.0"
LOG_DIR="/var/log/streamdb-maintenance"
LOG_FILE="$LOG_DIR/enhanced-proxy-maintenance-$(date +%Y%m%d-%H%M%S).log"
BACKUP_DIR="/var/backups/streamdb-maintenance"
LOCK_FILE="/var/run/streamdb-enhanced-proxy-maintenance.lock"
CONFIG_BACKUP_DIR="$BACKUP_DIR/nginx-configs"
SSL_BACKUP_DIR="$BACKUP_DIR/ssl-certs"
MAX_LOG_FILES=30
NOTIFICATION_EMAIL="<EMAIL>"

# Enhanced Configuration
DISK_USAGE_THRESHOLD=85
MEMORY_USAGE_THRESHOLD=90
LOAD_AVERAGE_THRESHOLD=2.0
SSL_CERT_EXPIRY_WARNING_DAYS=30
NGINX_CONFIG_BACKUP_RETENTION_DAYS=30
BACKEND_HEALTH_TIMEOUT=10
MAINTENANCE_WINDOW_HOURS=1

# Backend server details
BACKEND_SERVER="***********"
BACKEND_HEALTH_URL="http://${BACKEND_SERVER}:3001/api/health"
BACKEND_FASTPANEL_URL="http://${BACKEND_SERVER}:8888"

# Nginx configuration paths
NGINX_SITES_AVAILABLE="/etc/nginx/sites-available"
NGINX_SITES_ENABLED="/etc/nginx/sites-enabled"
NGINX_MAIN_CONFIG="/etc/nginx/nginx.conf"
STREAMDB_NGINX_CONFIG="$NGINX_SITES_AVAILABLE/streamdb.online"
FASTPANEL_NGINX_CONFIG="$NGINX_SITES_AVAILABLE/fastpanel.streamdb.online"

# SSL certificate paths
SSL_CERT_PATH="/etc/ssl/certs/cloudflare-origin.pem"
SSL_KEY_PATH="/etc/ssl/private/cloudflare-origin.key"

# Critical services for reverse proxy
CRITICAL_SERVICES=("nginx" "ufw" "ssh" "systemd-resolved")

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# ============================================================================
# ENHANCED UTILITY FUNCTIONS
# ============================================================================

log() {
    local level="$1"
    shift
    local message="$*"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    local color=""
    
    case "$level" in
        "ERROR") color="$RED" ;;
        "WARN") color="$YELLOW" ;;
        "SUCCESS") color="$GREEN" ;;
        "INFO") color="$BLUE" ;;
        "DEBUG") color="$PURPLE" ;;
        *) color="$NC" ;;
    esac
    
    echo -e "${color}${timestamp} [${level}] ${message}${NC}" | tee -a "$LOG_FILE"
}

log_info() { log "INFO" "$@"; }
log_warn() { log "WARN" "$@"; }
log_error() { log "ERROR" "$@"; }
log_success() { log "SUCCESS" "$@"; }
log_debug() { log "DEBUG" "$@"; }

print_header() {
    echo -e "${CYAN}============================================================================${NC}"
    echo -e "${CYAN}$1${NC}"
    echo -e "${CYAN}============================================================================${NC}"
    log_info "$1"
}

print_section() {
    echo -e "\n${YELLOW}--- $1 ---${NC}"
    log_info "Starting: $1"
}

check_root() {
    if [[ $EUID -ne 0 ]]; then
        log_error "This script must be run as root"
        exit 1
    fi
}

create_lock() {
    if [[ -f "$LOCK_FILE" ]]; then
        local pid=$(cat "$LOCK_FILE")
        if kill -0 "$pid" 2>/dev/null; then
            log_error "Enhanced proxy maintenance script is already running (PID: $pid)"
            exit 1
        else
            log_warn "Removing stale lock file"
            rm -f "$LOCK_FILE"
        fi
    fi
    echo $$ > "$LOCK_FILE"
    log_info "Created lock file: $LOCK_FILE"
}

remove_lock() {
    rm -f "$LOCK_FILE"
    log_info "Removed lock file"
}

setup_directories() {
    mkdir -p "$LOG_DIR" "$BACKUP_DIR" "$CONFIG_BACKUP_DIR" "$SSL_BACKUP_DIR"
    chmod 755 "$LOG_DIR" "$BACKUP_DIR" "$CONFIG_BACKUP_DIR" "$SSL_BACKUP_DIR"
    log_info "Created enhanced maintenance directories"
}

# ============================================================================
# ENHANCED MONITORING FUNCTIONS
# ============================================================================

check_system_resources() {
    print_section "Enhanced System Resource Monitoring"
    
    # Check disk usage
    local disk_usage=$(df / | awk 'NR==2 {print $5}' | sed 's/%//')
    log_info "Root filesystem usage: ${disk_usage}%"
    
    if [[ $disk_usage -gt $DISK_USAGE_THRESHOLD ]]; then
        log_warn "Disk usage (${disk_usage}%) exceeds threshold (${DISK_USAGE_THRESHOLD}%)"
        cleanup_nginx_logs
    fi
    
    # Check memory usage
    local memory_usage=$(free | awk 'NR==2{printf "%.0f", $3*100/$2}')
    log_info "Memory usage: ${memory_usage}%"
    
    if [[ $memory_usage -gt $MEMORY_USAGE_THRESHOLD ]]; then
        log_warn "Memory usage (${memory_usage}%) exceeds threshold (${MEMORY_USAGE_THRESHOLD}%)"
    fi
    
    # Check load average
    local load_avg=$(uptime | awk -F'load average:' '{print $2}' | awk '{print $2}' | sed 's/,//')
    log_info "5-minute load average: $load_avg"
    
    if (( $(echo "$load_avg > $LOAD_AVERAGE_THRESHOLD" | bc -l) )); then
        log_warn "Load average ($load_avg) exceeds threshold ($LOAD_AVERAGE_THRESHOLD)"
    fi
    
    # Check nginx worker processes
    local nginx_workers=$(pgrep -c nginx || echo "0")
    log_info "Nginx worker processes: $nginx_workers"
}

monitor_critical_services() {
    print_section "Critical Service Health Monitoring"
    
    local failed_services=()
    
    for service in "${CRITICAL_SERVICES[@]}"; do
        if systemctl is-active --quiet "$service"; then
            log_success "Service $service is running"
        else
            log_error "Critical service $service is not running"
            failed_services+=("$service")
        fi
    done
    
    # Check nginx configuration
    if nginx -t 2>/dev/null; then
        log_success "Nginx configuration is valid"
    else
        log_error "Nginx configuration has errors"
        failed_services+=("nginx-config")
    fi
    
    if [[ ${#failed_services[@]} -gt 0 ]]; then
        log_error "Failed services detected: ${failed_services[*]}"
        return 1
    fi
    
    return 0
}

check_backend_connectivity() {
    print_section "Enhanced Backend Server Connectivity"
    
    # Wait for backend server maintenance to complete
    log_info "Waiting for backend server maintenance to complete..."
    sleep 300  # 5 minutes
    
    # Check basic connectivity
    if ping -c 3 "$BACKEND_SERVER" >/dev/null 2>&1; then
        log_success "Backend server ($BACKEND_SERVER) is reachable"
    else
        log_error "Cannot reach backend server ($BACKEND_SERVER)"
        return 1
    fi
    
    # Check StreamDB application health
    if curl -s --max-time "$BACKEND_HEALTH_TIMEOUT" "$BACKEND_HEALTH_URL" >/dev/null 2>&1; then
        log_success "StreamDB application health check passed"
    else
        log_warn "StreamDB application health check failed or timeout"
    fi
    
    # Check FastPanel connectivity
    if curl -s --max-time "$BACKEND_HEALTH_TIMEOUT" "$BACKEND_FASTPANEL_URL" >/dev/null 2>&1; then
        log_success "FastPanel connectivity check passed"
    else
        log_warn "FastPanel connectivity check failed or timeout"
    fi
    
    return 0
}

check_ssl_certificates() {
    print_section "SSL Certificate Monitoring"
    
    if [[ -f "$SSL_CERT_PATH" ]]; then
        # Check certificate expiration
        local cert_expiry=$(openssl x509 -in "$SSL_CERT_PATH" -noout -enddate | cut -d= -f2)
        local cert_expiry_epoch=$(date -d "$cert_expiry" +%s)
        local current_epoch=$(date +%s)
        local days_until_expiry=$(( (cert_expiry_epoch - current_epoch) / 86400 ))
        
        log_info "SSL certificate expires in $days_until_expiry days ($cert_expiry)"
        
        if [[ $days_until_expiry -lt $SSL_CERT_EXPIRY_WARNING_DAYS ]]; then
            log_warn "SSL certificate expires in $days_until_expiry days - renewal needed soon"
        else
            log_success "SSL certificate is valid for $days_until_expiry days"
        fi
        
        # Verify certificate and key match
        local cert_hash=$(openssl x509 -in "$SSL_CERT_PATH" -noout -modulus | openssl md5)
        local key_hash=$(openssl rsa -in "$SSL_KEY_PATH" -noout -modulus | openssl md5)
        
        if [[ "$cert_hash" == "$key_hash" ]]; then
            log_success "SSL certificate and key match"
        else
            log_error "SSL certificate and key do not match"
        fi
    else
        log_warn "SSL certificate not found at $SSL_CERT_PATH"
    fi
}

check_nginx_performance() {
    print_section "Nginx Performance Monitoring"
    
    # Check nginx status if status module is enabled
    if curl -s http://localhost/nginx_status >/dev/null 2>&1; then
        local nginx_stats=$(curl -s http://localhost/nginx_status)
        log_info "Nginx status: $nginx_stats"
    fi
    
    # Check connection limits
    local current_connections=$(ss -tuln | grep -c ":80\|:443" || echo "0")
    log_info "Current HTTP/HTTPS connections: $current_connections"
    
    # Check error log for recent issues
    if [[ -f "/var/log/nginx/error.log" ]]; then
        local recent_errors=$(tail -100 /var/log/nginx/error.log | grep -c "$(date '+%Y/%m/%d')" || echo "0")
        log_info "Recent nginx errors today: $recent_errors"
    fi
}

# ============================================================================
# ENHANCED BACKUP FUNCTIONS
# ============================================================================

create_enhanced_nginx_backup() {
    print_section "Enhanced Nginx Configuration Backup"
    
    local backup_timestamp=$(date +%Y%m%d-%H%M%S)
    local nginx_backup_file="$CONFIG_BACKUP_DIR/nginx-config-backup-$backup_timestamp.tar.gz"
    
    # Create comprehensive nginx configuration backup
    tar -czf "$nginx_backup_file" \
        /etc/nginx/ \
        /etc/ssl/certs/cloudflare-origin.pem \
        /etc/ssl/private/cloudflare-origin.key \
        /etc/ufw/ \
        /etc/systemd/system/nginx.service.d/ \
        2>/dev/null || true
    
    if [[ -f "$nginx_backup_file" ]]; then
        log_success "Nginx configuration backup created: $nginx_backup_file"
        
        # Verify backup integrity
        if tar -tzf "$nginx_backup_file" >/dev/null 2>&1; then
            log_success "Nginx configuration backup integrity verified"
        else
            log_error "Nginx configuration backup integrity check failed"
            return 1
        fi
    else
        log_error "Failed to create nginx configuration backup"
        return 1
    fi
    
    return 0
}

# ============================================================================
# ENHANCED MAINTENANCE FUNCTIONS
# ============================================================================

perform_security_updates() {
    print_section "Enhanced Security Updates"

    # Update package lists
    log_info "Updating package lists..."
    if apt update; then
        log_success "Package lists updated successfully"
    else
        log_error "Failed to update package lists"
        return 1
    fi

    # Apply security updates
    log_info "Applying security updates..."
    if unattended-upgrade -d; then
        log_success "Security updates applied successfully"
    else
        log_warn "Some security updates may have failed"
    fi

    # Check for available updates
    local available_updates=$(apt list --upgradable 2>/dev/null | wc -l)
    log_info "Available package updates: $((available_updates - 1))"

    return 0
}

perform_safe_system_updates() {
    print_section "Safe System Updates"

    # Get list of safe updates (excluding nginx and critical packages)
    local safe_packages=$(apt list --upgradable 2>/dev/null | grep -v -E "(linux-|nginx-|ufw-|openssh-)" | awk -F'/' '{print $1}' | tail -n +2)

    if [[ -n "$safe_packages" ]]; then
        log_info "Applying safe system updates..."
        echo "$safe_packages" | xargs apt install -y
        log_success "Safe system updates completed"
    else
        log_info "No safe system updates available"
    fi

    return 0
}

cleanup_nginx_logs() {
    print_section "Nginx Log Cleanup"

    # Rotate nginx logs
    log_info "Rotating nginx logs..."
    nginx -s reopen

    # Compress old access logs
    find /var/log/nginx -name "*.log.1" -exec gzip {} \; 2>/dev/null || true

    # Remove old compressed logs (older than 30 days)
    find /var/log/nginx -name "*.gz" -mtime +30 -delete 2>/dev/null || true

    # Clean up large log files
    local large_logs=$(find /var/log/nginx -name "*.log" -size +100M)
    if [[ -n "$large_logs" ]]; then
        log_warn "Found large log files, truncating..."
        echo "$large_logs" | xargs truncate -s 10M
    fi

    log_success "Nginx log cleanup completed"
}

optimize_nginx_configuration() {
    print_section "Nginx Configuration Optimization"

    # Test current configuration
    if ! nginx -t; then
        log_error "Current nginx configuration has errors"
        return 1
    fi

    # Check for optimal worker processes
    local cpu_cores=$(nproc)
    local current_workers=$(grep -E "worker_processes" /etc/nginx/nginx.conf | awk '{print $2}' | sed 's/;//')

    log_info "CPU cores: $cpu_cores, Current nginx workers: $current_workers"

    # Reload nginx to apply any configuration changes
    if systemctl reload nginx; then
        log_success "Nginx configuration reloaded successfully"
    else
        log_error "Failed to reload nginx configuration"
        return 1
    fi

    return 0
}

check_firewall_rules() {
    print_section "Firewall Rules Verification"

    # Check UFW status
    local ufw_status=$(ufw status | head -1)
    log_info "UFW status: $ufw_status"

    if [[ "$ufw_status" == *"active"* ]]; then
        log_success "UFW firewall is active"

        # Check essential rules
        local ssh_rule=$(ufw status | grep -c "22/tcp" || echo "0")
        local http_rule=$(ufw status | grep -c "80/tcp" || echo "0")
        local https_rule=$(ufw status | grep -c "443/tcp" || echo "0")

        log_info "Firewall rules - SSH: $ssh_rule, HTTP: $http_rule, HTTPS: $https_rule"

        if [[ $ssh_rule -eq 0 ]]; then
            log_warn "SSH rule not found in firewall"
        fi
        if [[ $http_rule -eq 0 ]]; then
            log_warn "HTTP rule not found in firewall"
        fi
        if [[ $https_rule -eq 0 ]]; then
            log_warn "HTTPS rule not found in firewall"
        fi
    else
        log_warn "UFW firewall is not active"
    fi
}

perform_system_cleanup() {
    print_section "Enhanced System Cleanup"

    # Clean package cache
    log_info "Cleaning package cache..."
    apt autoremove -y
    apt autoclean

    # Clean temporary files
    log_info "Cleaning temporary files..."
    find /tmp -type f -atime +7 -delete 2>/dev/null || true
    find /var/tmp -type f -atime +7 -delete 2>/dev/null || true

    # Clean system logs
    log_info "Cleaning system logs..."
    journalctl --vacuum-time=30d

    # Clean old kernels
   # log_info "Cleaning old kernels..."
   # apt autoremove --purge -y

    log_success "System cleanup completed"
}

# ============================================================================
# ENHANCED RECOVERY FUNCTIONS
# ============================================================================

restore_service_states() {
    print_section "Service State Restoration"

    # Ensure all critical services are running
    for service in "${CRITICAL_SERVICES[@]}"; do
        if ! systemctl is-active --quiet "$service"; then
            log_warn "Attempting to restart $service"
            if systemctl restart "$service"; then
                log_success "Successfully restarted $service"

                # Special handling for nginx
                if [[ "$service" == "nginx" ]]; then
                    sleep 5  # Give nginx time to start
                    if nginx -t; then
                        log_success "Nginx configuration is valid after restart"
                    else
                        log_error "Nginx configuration has errors after restart"
                    fi
                fi
            else
                log_error "Failed to restart $service"
            fi
        fi
    done
}

cleanup_old_backups() {
    print_section "Backup Cleanup"

    # Clean old nginx configuration backups
    find "$CONFIG_BACKUP_DIR" -name "nginx-config-backup-*.tar.gz" -mtime +$NGINX_CONFIG_BACKUP_RETENTION_DAYS -delete 2>/dev/null || true
    log_info "Cleaned nginx configuration backups older than $NGINX_CONFIG_BACKUP_RETENTION_DAYS days"

    # Clean old maintenance logs
    find "$LOG_DIR" -name "enhanced-proxy-maintenance-*.log" -mtime +$MAX_LOG_FILES -delete 2>/dev/null || true
    log_info "Cleaned maintenance logs older than $MAX_LOG_FILES days"
}

perform_final_health_check() {
    print_section "Final Health Check"

    local health_issues=0

    # Check all critical services
    if ! monitor_critical_services; then
        health_issues=$((health_issues + 1))
    fi

    # Check backend connectivity
    if ! check_backend_connectivity; then
        health_issues=$((health_issues + 1))
    fi

    # Test nginx configuration
    if ! nginx -t 2>/dev/null; then
        log_error "Nginx configuration test failed"
        health_issues=$((health_issues + 1))
    fi

    # Test HTTP/HTTPS connectivity
    if curl -s --max-time 10 http://localhost >/dev/null 2>&1; then
        log_success "HTTP connectivity test passed"
    else
        log_warn "HTTP connectivity test failed"
        health_issues=$((health_issues + 1))
    fi

    if curl -s --max-time 10 https://localhost >/dev/null 2>&1; then
        log_success "HTTPS connectivity test passed"
    else
        log_warn "HTTPS connectivity test failed"
        health_issues=$((health_issues + 1))
    fi

    if [[ $health_issues -eq 0 ]]; then
        log_success "All health checks passed"
        return 0
    else
        log_warn "$health_issues health check(s) failed"
        return 1
    fi
}

# ============================================================================
# MAIN EXECUTION FUNCTION
# ============================================================================

main() {
    # Setup
    check_root
    setup_directories
    create_lock

    # Trap to ensure cleanup on exit
    trap 'remove_lock; exit' INT TERM EXIT

    print_header "$SCRIPT_NAME v$SCRIPT_VERSION - $(date)"
    log_info "Starting enhanced maintenance on backend2ndrevproxy (*************)"

    local start_time=$(date +%s)

    # Pre-maintenance checks
    check_system_resources

    if ! monitor_critical_services; then
        log_error "Critical service failures detected - aborting maintenance"
        exit 1
    fi

    check_ssl_certificates
    check_nginx_performance
    check_firewall_rules

    # Create backups
    if ! create_enhanced_nginx_backup; then
        log_error "Nginx configuration backup failed - aborting maintenance"
        exit 1
    fi

    # Perform maintenance
    perform_security_updates
    perform_safe_system_updates
    cleanup_nginx_logs
    optimize_nginx_configuration
    perform_system_cleanup

    # Post-maintenance tasks
    restore_service_states
    cleanup_old_backups

    # Final comprehensive health check
    sleep 10  # Give services time to stabilize
    if perform_final_health_check; then
        log_success "All systems are healthy after maintenance"
    else
        log_warn "Some systems may need attention after maintenance"
    fi

    # Verify backend connectivity after maintenance
    if check_backend_connectivity; then
        log_success "Backend connectivity verified after maintenance"
    else
        log_warn "Backend connectivity issues detected after maintenance"
    fi

    local end_time=$(date +%s)
    local duration=$((end_time - start_time))

    print_header "Enhanced Maintenance Completed Successfully"
    log_success "Reverse proxy maintenance completed in ${duration} seconds at $(date)"

    # Remove lock file
    remove_lock
}

# Execute main function
main "$@"
