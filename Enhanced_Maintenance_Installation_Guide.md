# 🚀 StreamDB Enhanced Automated Maintenance Installation Guide

## 📋 OVERVIEW

This guide provides comprehensive instructions for deploying enhanced automated maintenance scripts on your 4-tier reverse proxy infrastructure. These scripts provide advanced monitoring, security, and maintenance capabilities beyond standard managed hosting solutions.

### Enhanced Features
- ✅ **Advanced Resource Monitoring** - CPU, memory, disk, and network monitoring with configurable thresholds
- ✅ **Enhanced Security Updates** - Automated security patching with rollback capabilities
- ✅ **SSL Certificate Monitoring** - Automatic expiration tracking and validation
- ✅ **Database Optimization** - Automated table optimization and integrity checks
- ✅ **Performance Monitoring** - Nginx performance metrics and optimization
- ✅ **Comprehensive Backup** - Multi-tier backup system with integrity verification
- ✅ **Intelligent Recovery** - Advanced service restoration and health checks
- ✅ **Detailed Logging** - Color-coded logs with multiple severity levels

### Architecture Overview
```
Backend Server (***********)          Reverse Proxy (*************)
- Enhanced Maintenance: Thu 00:00      - Enhanced Maintenance: Thu 00:30
- FastPanel, MySQL, PM2, StreamDB      - Nginx, SSL, Firewall, Load Balancing
- Database optimization                - SSL monitoring
- Performance monitoring               - Performance optimization
```

---

## 📁 REQUIRED FILES

Ensure you have these enhanced scripts:

1. **enhanced-backend-maintenance.sh** - Enhanced backend server maintenance
2. **enhanced-reverse-proxy-maintenance.sh** - Enhanced reverse proxy maintenance
3. **enhanced-cron-configuration.sh** - Enhanced cron job setup
4. **enhanced-validation-scripts.sh** - Enhanced testing and validation

---

## 🖥️ PHASE 1: ENHANCED BACKEND SERVER INSTALLATION (***********)

### Step 1.1: Connect to Backend Server
```bash
ssh root@***********
```

### Step 1.2: Install Enhanced Prerequisites
```bash
# Update system
apt update && apt upgrade -y

# Install required packages for enhanced features
apt install -y \
    unattended-upgrades \
    curl \
    wget \
    logrotate \
    bc \
    jq \
    htop \
    iotop \
    nethogs \
    mysql-client \
    gzip \
    tar

# Verify PM2 is installed and working
pm2 --version
pm2 list

# If PM2 is not installed:
npm install -g pm2
```

### Step 1.3: Configure Enhanced Unattended Upgrades
```bash
# Create enhanced unattended upgrades configuration
cat > /etc/apt/apt.conf.d/50unattended-upgrades << 'EOF'
Unattended-Upgrade::Allowed-Origins {
    "${distro_id}:${distro_codename}-security";
    "${distro_id}ESMApps:${distro_codename}-apps-security";
    "${distro_id}ESM:${distro_codename}-infra-security";
};

Unattended-Upgrade::Package-Blacklist {
    "mysql-server*";
    "nginx*";
    "fastpanel*";
    "linux-image*";
    "linux-headers*";
};

Unattended-Upgrade::AutoFixInterruptedDpkg "true";
Unattended-Upgrade::MinimalSteps "true";
Unattended-Upgrade::Remove-Unused-Dependencies "true";
Unattended-Upgrade::Automatic-Reboot "false";
Unattended-Upgrade::Mail "<EMAIL>";
Unattended-Upgrade::MailOnlyOnError "true";
EOF

# Enable automatic updates with enhanced settings
cat > /etc/apt/apt.conf.d/20auto-upgrades << 'EOF'
APT::Periodic::Update-Package-Lists "1";
APT::Periodic::Download-Upgradeable-Packages "1";
APT::Periodic::AutocleanInterval "7";
APT::Periodic::Unattended-Upgrade "1";
EOF
```

### Step 1.4: Upload and Install Enhanced Backend Script
```bash
# Create enhanced directory structure
mkdir -p /usr/local/bin
mkdir -p /var/log/streamdb-maintenance
mkdir -p /var/backups/streamdb-maintenance/{configs,database}
mkdir -p /etc/streamdb-maintenance

# Upload the enhanced backend maintenance script
# Method 1: Using SCP from your local machine
scp enhanced-backend-maintenance.sh root@***********:/usr/local/bin/

# Method 2: Using FastPanel File Manager
# Upload to /usr/local/bin/ via FastPanel interface

# Set proper permissions
chmod +x /usr/local/bin/enhanced-backend-maintenance.sh
chown root:root /usr/local/bin/enhanced-backend-maintenance.sh

# Create configuration file for customization
cat > /etc/streamdb-maintenance/backend-config.conf << 'EOF'
# Enhanced Backend Maintenance Configuration
DISK_USAGE_THRESHOLD=85
MEMORY_USAGE_THRESHOLD=90
LOAD_AVERAGE_THRESHOLD=4.0
DB_BACKUP_RETENTION_DAYS=14
CONFIG_BACKUP_RETENTION_DAYS=30
NOTIFICATION_EMAIL="<EMAIL>"
EOF
```

### Step 1.5: Test Enhanced Backend Script
```bash
# Run a dry-run test (if available)
/usr/local/bin/enhanced-backend-maintenance.sh --test-mode

# Check script syntax
bash -n /usr/local/bin/enhanced-backend-maintenance.sh

# Verify all dependencies are available
which mysql mysqldump pm2 jq bc

# Test database connectivity
mysql -u stream_db_admin -e "SELECT 1;" stream_db
```

---

## 🔄 PHASE 2: ENHANCED REVERSE PROXY INSTALLATION (*************)

### Step 2.1: Connect to Reverse Proxy Server
```bash
ssh root@*************
```

### Step 2.2: Install Enhanced Prerequisites
```bash
# Update system
apt update && apt upgrade -y

# Install required packages for enhanced features
apt install -y \
    unattended-upgrades \
    curl \
    wget \
    logrotate \
    bc \
    openssl \
    htop \
    iotop \
    nethogs \
    gzip \
    tar

# Verify Nginx is installed and running
systemctl status nginx
nginx -v
nginx -t
```

### Step 2.3: Configure Enhanced SSL Monitoring
```bash
# Create SSL monitoring directory
mkdir -p /var/log/ssl-monitoring

# Create SSL certificate monitoring script
cat > /usr/local/bin/check-ssl-expiry.sh << 'EOF'
#!/bin/bash
SSL_CERT_PATH="/etc/ssl/certs/cloudflare-origin.pem"
if [[ -f "$SSL_CERT_PATH" ]]; then
    EXPIRY_DATE=$(openssl x509 -in "$SSL_CERT_PATH" -noout -enddate | cut -d= -f2)
    EXPIRY_EPOCH=$(date -d "$EXPIRY_DATE" +%s)
    CURRENT_EPOCH=$(date +%s)
    DAYS_UNTIL_EXPIRY=$(( (EXPIRY_EPOCH - CURRENT_EPOCH) / 86400 ))
    echo "SSL certificate expires in $DAYS_UNTIL_EXPIRY days"
    if [[ $DAYS_UNTIL_EXPIRY -lt 30 ]]; then
        echo "WARNING: SSL certificate expires soon!"
    fi
else
    echo "SSL certificate not found"
fi
EOF

chmod +x /usr/local/bin/check-ssl-expiry.sh
```

### Step 2.4: Upload and Install Enhanced Reverse Proxy Script
```bash
# Create enhanced directory structure
mkdir -p /usr/local/bin
mkdir -p /var/log/streamdb-maintenance
mkdir -p /var/backups/streamdb-maintenance/{nginx-configs,ssl-certs}
mkdir -p /etc/streamdb-maintenance

# Upload the enhanced reverse proxy maintenance script
scp enhanced-reverse-proxy-maintenance.sh root@*************:/usr/local/bin/

# Set proper permissions
chmod +x /usr/local/bin/enhanced-reverse-proxy-maintenance.sh
chown root:root /usr/local/bin/enhanced-reverse-proxy-maintenance.sh

# Create configuration file
cat > /etc/streamdb-maintenance/proxy-config.conf << 'EOF'
# Enhanced Reverse Proxy Maintenance Configuration
DISK_USAGE_THRESHOLD=85
MEMORY_USAGE_THRESHOLD=90
LOAD_AVERAGE_THRESHOLD=2.0
SSL_CERT_EXPIRY_WARNING_DAYS=30
NGINX_CONFIG_BACKUP_RETENTION_DAYS=30
BACKEND_HEALTH_TIMEOUT=10
NOTIFICATION_EMAIL="<EMAIL>"
EOF
```

### Step 2.5: Test Enhanced Reverse Proxy Script
```bash
# Test nginx configuration
nginx -t

# Test SSL certificate
/usr/local/bin/check-ssl-expiry.sh

# Test backend connectivity
curl -s --max-time 10 http://***********:3001/api/health

# Run script syntax check
bash -n /usr/local/bin/enhanced-reverse-proxy-maintenance.sh
```

---

## ⚙️ PHASE 3: ENHANCED CRON CONFIGURATION

### Step 3.1: Create Enhanced Cron Configuration Script
```bash
# Create enhanced cron configuration script
cat > /usr/local/bin/enhanced-cron-configuration.sh << 'EOF'
#!/bin/bash

# Enhanced Cron Configuration for StreamDB Maintenance

set -euo pipefail

# Detect server type
if [[ -f "/var/www/streamdb_onl_usr/data/www/streamdb.online/.env" ]]; then
    SERVER_TYPE="backend"
    SCRIPT_PATH="/usr/local/bin/enhanced-backend-maintenance.sh"
    CRON_TIME="0 0 * * 4"  # Thursday 00:00
elif [[ -f "/etc/nginx/sites-available/streamdb.online" ]]; then
    SERVER_TYPE="proxy"
    SCRIPT_PATH="/usr/local/bin/enhanced-reverse-proxy-maintenance.sh"
    CRON_TIME="30 0 * * 4"  # Thursday 00:30
else
    echo "Error: Cannot detect server type"
    exit 1
fi

echo "Configuring enhanced cron job for $SERVER_TYPE server"

# Create cron environment file
cat > /etc/cron.d/streamdb-enhanced-maintenance << EOF
# Enhanced StreamDB Maintenance Cron Job
SHELL=/bin/bash
PATH=/usr/local/sbin:/usr/local/bin:/sbin:/bin:/usr/sbin:/usr/bin
MAILTO=""

# Enhanced maintenance schedule
$CRON_TIME root $SCRIPT_PATH >> /var/log/streamdb-maintenance/cron-enhanced.log 2>&1
EOF

# Set proper permissions
chmod 644 /etc/cron.d/streamdb-enhanced-maintenance

# Restart cron service
systemctl restart cron

echo "Enhanced cron job configured successfully"
echo "Schedule: $CRON_TIME"
echo "Script: $SCRIPT_PATH"
echo "Log: /var/log/streamdb-maintenance/cron-enhanced.log"
EOF

chmod +x /usr/local/bin/enhanced-cron-configuration.sh
```

### Step 3.2: Install Cron Jobs on Both Servers

**On Backend Server (***********):**
```bash
/usr/local/bin/enhanced-cron-configuration.sh

# Verify cron job installation
crontab -l
cat /etc/cron.d/streamdb-enhanced-maintenance
```

**On Reverse Proxy Server (*************):**
```bash
/usr/local/bin/enhanced-cron-configuration.sh

# Verify cron job installation
crontab -l
cat /etc/cron.d/streamdb-enhanced-maintenance
```

---

## 🧪 PHASE 4: ENHANCED VALIDATION AND TESTING

### Step 4.1: Create Enhanced Validation Script
```bash
# Create comprehensive validation script
cat > /usr/local/bin/enhanced-validation-scripts.sh << 'EOF'
#!/bin/bash

# Enhanced Validation Script for StreamDB Maintenance

set -euo pipefail

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() { echo -e "${BLUE}[INFO]${NC} $*"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $*"; }
log_warn() { echo -e "${YELLOW}[WARN]${NC} $*"; }
log_error() { echo -e "${RED}[ERROR]${NC} $*"; }

# Detect server type
if [[ -f "/var/www/streamdb_onl_usr/data/www/streamdb.online/.env" ]]; then
    SERVER_TYPE="backend"
    SCRIPT_PATH="/usr/local/bin/enhanced-backend-maintenance.sh"
elif [[ -f "/etc/nginx/sites-available/streamdb.online" ]]; then
    SERVER_TYPE="proxy"
    SCRIPT_PATH="/usr/local/bin/enhanced-reverse-proxy-maintenance.sh"
else
    log_error "Cannot detect server type"
    exit 1
fi

log_info "Running enhanced validation for $SERVER_TYPE server"

# Test 1: Script existence and permissions
if [[ -f "$SCRIPT_PATH" && -x "$SCRIPT_PATH" ]]; then
    log_success "Maintenance script exists and is executable"
else
    log_error "Maintenance script not found or not executable: $SCRIPT_PATH"
    exit 1
fi

# Test 2: Script syntax
if bash -n "$SCRIPT_PATH"; then
    log_success "Script syntax is valid"
else
    log_error "Script syntax errors detected"
    exit 1
fi

# Test 3: Required directories
REQUIRED_DIRS=(
    "/var/log/streamdb-maintenance"
    "/var/backups/streamdb-maintenance"
    "/etc/streamdb-maintenance"
)

for dir in "${REQUIRED_DIRS[@]}"; do
    if [[ -d "$dir" ]]; then
        log_success "Directory exists: $dir"
    else
        log_error "Required directory missing: $dir"
        exit 1
    fi
done

# Test 4: Required commands
REQUIRED_COMMANDS=("curl" "wget" "bc" "tar" "gzip")

if [[ "$SERVER_TYPE" == "backend" ]]; then
    REQUIRED_COMMANDS+=("mysql" "mysqldump" "pm2" "jq")
elif [[ "$SERVER_TYPE" == "proxy" ]]; then
    REQUIRED_COMMANDS+=("nginx" "openssl")
fi

for cmd in "${REQUIRED_COMMANDS[@]}"; do
    if command -v "$cmd" >/dev/null 2>&1; then
        log_success "Command available: $cmd"
    else
        log_error "Required command missing: $cmd"
        exit 1
    fi
done

# Test 5: Service-specific tests
if [[ "$SERVER_TYPE" == "backend" ]]; then
    # Test database connectivity
    if mysql -u stream_db_admin -e "SELECT 1;" stream_db >/dev/null 2>&1; then
        log_success "Database connectivity test passed"
    else
        log_error "Database connectivity test failed"
        exit 1
    fi

    # Test PM2 status
    if pm2 list >/dev/null 2>&1; then
        log_success "PM2 is accessible"
    else
        log_error "PM2 is not accessible"
        exit 1
    fi

elif [[ "$SERVER_TYPE" == "proxy" ]]; then
    # Test nginx configuration
    if nginx -t >/dev/null 2>&1; then
        log_success "Nginx configuration is valid"
    else
        log_error "Nginx configuration has errors"
        exit 1
    fi

    # Test SSL certificate
    if [[ -f "/etc/ssl/certs/cloudflare-origin.pem" ]]; then
        log_success "SSL certificate found"
    else
        log_warn "SSL certificate not found (may be normal for some setups)"
    fi
fi

# Test 6: Cron job configuration
if [[ -f "/etc/cron.d/streamdb-enhanced-maintenance" ]]; then
    log_success "Enhanced cron job configuration found"
else
    log_error "Enhanced cron job configuration missing"
    exit 1
fi

log_success "All enhanced validation tests passed!"
log_info "Enhanced maintenance system is ready for deployment"
EOF

chmod +x /usr/local/bin/enhanced-validation-scripts.sh
```

### Step 4.2: Run Validation Tests

**On Backend Server:**
```bash
/usr/local/bin/enhanced-validation-scripts.sh
```

**On Reverse Proxy Server:**
```bash
/usr/local/bin/enhanced-validation-scripts.sh
```

### Step 4.3: Manual Test Run (Optional)
```bash
# Run maintenance script manually to test (use with caution)
# This will perform actual maintenance operations

# Backend server test
/usr/local/bin/enhanced-backend-maintenance.sh

# Reverse proxy server test
/usr/local/bin/enhanced-reverse-proxy-maintenance.sh
```
