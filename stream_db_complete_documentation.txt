=== STREAM_DB COMPLETE DATABASE DOCUMENTATION ===
Generated: Mon Jul 21 16:56:28 UTC 2025
Server: backend1maindb
MySQL Version: mysql  Ver 8.0.42-0ubuntu0.24.04.1 for Linux on x86_64 ((Ubuntu))

=== 1. DATABASE OVERVIEW ===
mysql: [Warning] Using a password on the command line interface can be insecure.
total_tables
17
TABLE_NAME	TABLE_ROWS	size_mb
ad_blocker_tracking	87	0.11
admin_security_logs	210	0.13
admin_users	1	0.09
auth_tokens	0	0.11
categories	18	0.09
content	9	0.20
content_section_mappings	60	0.06
content_sections	6	0.11
episodes	8	0.08
login_attempts	0	0.09
password_reset_tokens	0	0.09
seasons	4	0.06
section_categories	41	0.06
section_content_types	0	0.06
security_logs	1158	0.63
sessions	0	0.02
user_sessions	0	0.06

=== 2. COMPLETE TABLE STRUCTURES ===
mysqldump: [Warning] Using a password on the command line interface can be insecure.
-- MySQL dump 10.13  Distrib 8.0.42, for Linux (x86_64)
--
-- Host: localhost    Database: stream_db
-- ------------------------------------------------------
-- Server version	8.0.42-0ubuntu0.24.04.1

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;
mysqldump: Error: 'Access denied; you need (at least one of) the PROCESS privilege(s) for this operation' when trying to dump tablespaces

--
-- Table structure for table `ad_blocker_tracking`
--

DROP TABLE IF EXISTS `ad_blocker_tracking`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `ad_blocker_tracking` (
  `id` int NOT NULL AUTO_INCREMENT,
  `session_id` varchar(255) NOT NULL,
  `user_id` varchar(255) DEFAULT NULL,
  `last_shown_timestamp` bigint NOT NULL DEFAULT '0',
  `dismiss_count` int NOT NULL DEFAULT '0',
  `user_agent` text,
  `ip_address` varchar(45) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_session` (`session_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_last_shown` (`last_shown_timestamp`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB AUTO_INCREMENT=109 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `admin_security_logs`
--

DROP TABLE IF EXISTS `admin_security_logs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `admin_security_logs` (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` int DEFAULT NULL,
  `action` varchar(100) NOT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text,
  `details` json DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_action` (`action`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB AUTO_INCREMENT=278 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `admin_users`
--

DROP TABLE IF EXISTS `admin_users`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `admin_users` (
  `id` int NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL,
  `password_hash` varchar(255) NOT NULL,
  `email` varchar(100) DEFAULT NULL,
  `role` enum('admin','moderator') DEFAULT 'admin',
  `permissions` json DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT '1',
  `failed_login_attempts` int DEFAULT '0',
  `last_login` timestamp NULL DEFAULT NULL,
  `login_attempts` int DEFAULT '0',
  `locked_until` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`),
  UNIQUE KEY `email` (`email`),
  KEY `idx_username` (`username`),
  KEY `idx_email` (`email`),
  KEY `idx_active` (`is_active`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `auth_tokens`
--

DROP TABLE IF EXISTS `auth_tokens`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `auth_tokens` (
  `id` int NOT NULL AUTO_INCREMENT,
  `token_hash` varchar(255) NOT NULL,
  `session_id` varchar(255) NOT NULL,
  `user_id` varchar(255) NOT NULL,
  `token_type` enum('access','refresh') NOT NULL DEFAULT 'access',
  `expires_at` timestamp NOT NULL,
  `is_revoked` tinyint(1) NOT NULL DEFAULT '0',
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_token_hash` (`token_hash`),
  KEY `idx_session_id` (`session_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_token_type` (`token_type`),
  KEY `idx_expires_at` (`expires_at`),
  KEY `idx_is_revoked` (`is_revoked`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `categories`
--

DROP TABLE IF EXISTS `categories`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `categories` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `type` enum('movie','series','both') DEFAULT 'both',
  `slug` varchar(100) NOT NULL,
  `description` text,
  `is_active` tinyint(1) DEFAULT '1',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`),
  UNIQUE KEY `slug` (`slug`),
  KEY `idx_type` (`type`),
  KEY `idx_active` (`is_active`),
  KEY `idx_slug` (`slug`)
) ENGINE=InnoDB AUTO_INCREMENT=22 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `content`
--

DROP TABLE IF EXISTS `content`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `content` (
  `id` varchar(50) NOT NULL,
  `title` varchar(255) NOT NULL,
  `description` text,
  `year` int NOT NULL,
  `type` enum('movie','series','requested') NOT NULL,
  `category` varchar(100) DEFAULT NULL,
  `section_id` int DEFAULT NULL,
  `image` varchar(500) DEFAULT NULL,
  `cover_image` varchar(500) DEFAULT NULL,
  `tmdb_id` varchar(20) DEFAULT NULL,
  `poster_url` varchar(500) DEFAULT NULL,
  `thumbnail_url` varchar(500) DEFAULT NULL,
  `secure_video_links` text,
  `imdb_rating` decimal(3,1) DEFAULT NULL,
  `runtime` varchar(20) DEFAULT NULL,
  `studio` varchar(255) DEFAULT NULL,
  `tags` text,
  `trailer` varchar(500) DEFAULT NULL,
  `subtitle_url` varchar(500) DEFAULT NULL,
  `is_published` tinyint(1) DEFAULT '0',
  `is_featured` tinyint(1) DEFAULT '0',
  `add_to_carousel` tinyint(1) DEFAULT '0',
  `carousel_position` int DEFAULT NULL,
  `crop_settings` json DEFAULT NULL,
  `total_seasons` int DEFAULT '0',
  `total_episodes` int DEFAULT '0',
  `languages` json DEFAULT NULL,
  `genres` json DEFAULT NULL,
  `quality` json DEFAULT NULL,
  `quality_label` varchar(100) DEFAULT NULL,
  `audio_tracks` json DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_type` (`type`),
  KEY `idx_year` (`year`),
  KEY `idx_published` (`is_published`),
  KEY `idx_featured` (`is_featured`),
  KEY `idx_carousel` (`add_to_carousel`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_tmdb_id` (`tmdb_id`),
  KEY `idx_category` (`category`),
  KEY `idx_section` (`section_id`),
  KEY `idx_content_quality_label` (`quality_label`),
  KEY `idx_carousel_position` (`carousel_position`),
  FULLTEXT KEY `ft_search` (`title`,`description`,`tags`),
  CONSTRAINT `content_ibfk_1` FOREIGN KEY (`section_id`) REFERENCES `content_sections` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `content_section_mappings`
--

DROP TABLE IF EXISTS `content_section_mappings`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `content_section_mappings` (
  `id` int NOT NULL AUTO_INCREMENT,
  `content_id` varchar(50) NOT NULL,
  `section_id` int NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_content_section` (`content_id`,`section_id`),
  KEY `idx_content_id` (`content_id`),
  KEY `idx_section_id` (`section_id`),
  CONSTRAINT `content_section_mappings_ibfk_1` FOREIGN KEY (`content_id`) REFERENCES `content` (`id`) ON DELETE CASCADE,
  CONSTRAINT `content_section_mappings_ibfk_2` FOREIGN KEY (`section_id`) REFERENCES `content_sections` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=187 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `content_sections`
--

DROP TABLE IF EXISTS `content_sections`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `content_sections` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `slug` varchar(100) NOT NULL,
  `description` text,
  `icon` varchar(50) DEFAULT NULL,
  `color` varchar(20) DEFAULT NULL,
  `display_order` int DEFAULT '0',
  `is_active` tinyint(1) DEFAULT '1',
  `show_in_navigation` tinyint(1) DEFAULT '1',
  `show_on_homepage` tinyint(1) DEFAULT '1',
  `max_items_homepage` int DEFAULT '20',
  `content_types` json DEFAULT NULL,
  `filter_rules` json DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`),
  UNIQUE KEY `slug` (`slug`),
  KEY `idx_active` (`is_active`),
  KEY `idx_slug` (`slug`),
  KEY `idx_display_order` (`display_order`),
  KEY `idx_homepage` (`show_on_homepage`)
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `episodes`
--

DROP TABLE IF EXISTS `episodes`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `episodes` (
  `id` varchar(50) NOT NULL,
  `season_id` varchar(50) NOT NULL,
  `content_id` varchar(50) NOT NULL,
  `episode_number` int NOT NULL,
  `title` varchar(255) NOT NULL,
  `description` text,
  `secure_video_links` text NOT NULL,
  `runtime` varchar(20) DEFAULT NULL,
  `air_date` date DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_episode_per_season` (`season_id`,`episode_number`),
  KEY `idx_season_id` (`season_id`),
  KEY `idx_content_id` (`content_id`),
  KEY `idx_episode_number` (`episode_number`),
  CONSTRAINT `episodes_ibfk_1` FOREIGN KEY (`season_id`) REFERENCES `seasons` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `episodes_ibfk_2` FOREIGN KEY (`content_id`) REFERENCES `content` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = '' */ ;
DELIMITER ;;
/*!50003 CREATE*/ /*!50017 DEFINER=`stream_db_admin`@`localhost`*/ /*!50003 TRIGGER `update_season_episode_count_after_insert` AFTER INSERT ON `episodes` FOR EACH ROW BEGIN
    UPDATE seasons 
    SET episode_count = (
        SELECT COUNT(*) 
        FROM episodes 
        WHERE season_id = NEW.season_id
    )
    WHERE id = NEW.season_id;
    
    UPDATE content 
    SET total_episodes = (
        SELECT COUNT(*) 
        FROM episodes 
        WHERE content_id = NEW.content_id
    )
    WHERE id = NEW.content_id;
END */;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = '' */ ;
DELIMITER ;;
/*!50003 CREATE*/ /*!50017 DEFINER=`stream_db_admin`@`localhost`*/ /*!50003 TRIGGER `update_season_episode_count_after_update` AFTER UPDATE ON `episodes` FOR EACH ROW BEGIN
    IF OLD.season_id != NEW.season_id OR OLD.content_id != NEW.content_id THEN
        UPDATE seasons 
        SET episode_count = (
            SELECT COUNT(*) 
            FROM episodes 
            WHERE season_id = OLD.season_id
        )
        WHERE id = OLD.season_id;
        
        UPDATE seasons 
        SET episode_count = (
            SELECT COUNT(*) 
            FROM episodes 
            WHERE season_id = NEW.season_id
        )
        WHERE id = NEW.season_id;
        
        UPDATE content 
        SET total_episodes = (
            SELECT COUNT(*) 
            FROM episodes 
            WHERE content_id = OLD.content_id
        )
        WHERE id = OLD.content_id;
        
        UPDATE content 
        SET total_episodes = (
            SELECT COUNT(*) 
            FROM episodes 
            WHERE content_id = NEW.content_id
        )
        WHERE id = NEW.content_id;
    END IF;
END */;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = '' */ ;
DELIMITER ;;
/*!50003 CREATE*/ /*!50017 DEFINER=`stream_db_admin`@`localhost`*/ /*!50003 TRIGGER `update_season_episode_count_after_delete` AFTER DELETE ON `episodes` FOR EACH ROW BEGIN
    UPDATE seasons 
    SET episode_count = (
        SELECT COUNT(*) 
        FROM episodes 
        WHERE season_id = OLD.season_id
    )
    WHERE id = OLD.season_id;
    
    UPDATE content 
    SET total_episodes = (
        SELECT COUNT(*) 
        FROM episodes 
        WHERE content_id = OLD.content_id
    )
    WHERE id = OLD.content_id;
END */;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;

--
-- Table structure for table `login_attempts`
--

DROP TABLE IF EXISTS `login_attempts`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `login_attempts` (
  `id` int NOT NULL AUTO_INCREMENT,
  `session_id` varchar(255) NOT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text,
  `username` varchar(255) DEFAULT NULL,
  `success` tinyint(1) NOT NULL DEFAULT '0',
  `failure_reason` varchar(100) DEFAULT NULL,
  `timestamp` bigint NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_session_id` (`session_id`),
  KEY `idx_ip_address` (`ip_address`),
  KEY `idx_timestamp` (`timestamp`),
  KEY `idx_success` (`success`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB AUTO_INCREMENT=42 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `password_reset_tokens`
--

DROP TABLE IF EXISTS `password_reset_tokens`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `password_reset_tokens` (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` int NOT NULL,
  `token` varchar(255) NOT NULL,
  `expires_at` timestamp NOT NULL,
  `used` tinyint(1) DEFAULT '0',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `used_at` timestamp NULL DEFAULT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text,
  PRIMARY KEY (`id`),
  UNIQUE KEY `token` (`token`),
  KEY `idx_token` (`token`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_expires_at` (`expires_at`),
  KEY `idx_used` (`used`),
  CONSTRAINT `password_reset_tokens_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `admin_users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `seasons`
--

DROP TABLE IF EXISTS `seasons`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `seasons` (
  `id` varchar(50) NOT NULL,
  `content_id` varchar(50) NOT NULL,
  `web_series_title` varchar(255) DEFAULT NULL,
  `season_number` int NOT NULL,
  `title` varchar(255) DEFAULT NULL,
  `description` text,
  `episode_count` int DEFAULT '0',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_season_per_content` (`content_id`,`season_number`),
  KEY `idx_content_id` (`content_id`),
  KEY `idx_season_number` (`season_number`),
  CONSTRAINT `seasons_ibfk_1` FOREIGN KEY (`content_id`) REFERENCES `content` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `section_categories`
--

DROP TABLE IF EXISTS `section_categories`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `section_categories` (
  `id` int NOT NULL AUTO_INCREMENT,
  `section_id` int NOT NULL,
  `category_id` int NOT NULL,
  `is_default` tinyint(1) DEFAULT '0',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_section_category` (`section_id`,`category_id`),
  KEY `idx_section` (`section_id`),
  KEY `idx_category` (`category_id`),
  CONSTRAINT `section_categories_ibfk_1` FOREIGN KEY (`section_id`) REFERENCES `content_sections` (`id`) ON DELETE CASCADE,
  CONSTRAINT `section_categories_ibfk_2` FOREIGN KEY (`category_id`) REFERENCES `categories` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=228 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `section_content_types`
--

DROP TABLE IF EXISTS `section_content_types`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `section_content_types` (
  `id` int NOT NULL AUTO_INCREMENT,
  `section_id` int NOT NULL,
  `type_name` varchar(50) NOT NULL,
  `type_label` varchar(100) NOT NULL,
  `description` text,
  `fields_config` json DEFAULT NULL,
  `validation_rules` json DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT '1',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_section_type` (`section_id`,`type_name`),
  KEY `idx_section` (`section_id`),
  KEY `idx_active` (`is_active`),
  CONSTRAINT `section_content_types_ibfk_1` FOREIGN KEY (`section_id`) REFERENCES `content_sections` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `security_logs`
--

DROP TABLE IF EXISTS `security_logs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `security_logs` (
  `id` int NOT NULL AUTO_INCREMENT,
  `session_id` varchar(255) NOT NULL,
  `user_id` varchar(255) DEFAULT NULL,
  `event_type` enum('LOGIN_SUCCESS','LOGIN_FAILED','LOGOUT','SESSION_EXPIRED','SESSION_REFRESHED','ACCOUNT_LOCKED','SECURITY_VIOLATION') NOT NULL,
  `severity` enum('low','medium','high','critical') NOT NULL DEFAULT 'medium',
  `details` json DEFAULT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text,
  `timestamp` bigint NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_session_id` (`session_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_event_type` (`event_type`),
  KEY `idx_severity` (`severity`),
  KEY `idx_timestamp` (`timestamp`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB AUTO_INCREMENT=2074 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `sessions`
--

DROP TABLE IF EXISTS `sessions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sessions` (
  `session_id` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `expires` int unsigned NOT NULL,
  `data` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin,
  PRIMARY KEY (`session_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `user_sessions`
--

DROP TABLE IF EXISTS `user_sessions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `user_sessions` (
  `id` varchar(255) NOT NULL,
  `user_id` varchar(255) DEFAULT NULL,
  `session_data` json NOT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text,
  `expires_at` timestamp NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_expires_at` (`expires_at`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-07-21 16:56:28

=== 3. FOREIGN KEY RELATIONSHIPS ===
mysql: [Warning] Using a password on the command line interface can be insecure.
TABLE_NAME	COLUMN_NAME	REFERENCED_TABLE_NAME	REFERENCED_COLUMN_NAME	DELETE_RULE	UPDATE_RULE
content	section_id	content_sections	id	SET NULL	NO ACTION
content_section_mappings	content_id	content	id	CASCADE	NO ACTION
content_section_mappings	section_id	content_sections	id	CASCADE	NO ACTION
episodes	season_id	seasons	id	CASCADE	CASCADE
episodes	content_id	content	id	CASCADE	CASCADE
password_reset_tokens	user_id	admin_users	id	CASCADE	NO ACTION
seasons	content_id	content	id	CASCADE	CASCADE
section_categories	section_id	content_sections	id	CASCADE	NO ACTION
section_categories	category_id	categories	id	CASCADE	NO ACTION
section_content_types	section_id	content_sections	id	CASCADE	NO ACTION

=== 4. INDEX INFORMATION ===
mysql: [Warning] Using a password on the command line interface can be insecure.
TABLE_NAME	INDEX_NAME	COLUMN_NAME	NON_UNIQUE	INDEX_TYPE
ad_blocker_tracking	idx_created_at	created_at	1	BTREE
ad_blocker_tracking	idx_last_shown	last_shown_timestamp	1	BTREE
ad_blocker_tracking	idx_user_id	user_id	1	BTREE
ad_blocker_tracking	PRIMARY	id	0	BTREE
ad_blocker_tracking	unique_session	session_id	0	BTREE
admin_security_logs	idx_action	action	1	BTREE
admin_security_logs	idx_created_at	created_at	1	BTREE
admin_security_logs	idx_user_id	user_id	1	BTREE
admin_security_logs	PRIMARY	id	0	BTREE
admin_users	email	email	0	BTREE
admin_users	idx_active	is_active	1	BTREE
admin_users	idx_email	email	1	BTREE
admin_users	idx_username	username	1	BTREE
admin_users	PRIMARY	id	0	BTREE
admin_users	username	username	0	BTREE
auth_tokens	idx_expires_at	expires_at	1	BTREE
auth_tokens	idx_is_revoked	is_revoked	1	BTREE
auth_tokens	idx_session_id	session_id	1	BTREE
auth_tokens	idx_token_type	token_type	1	BTREE
auth_tokens	idx_user_id	user_id	1	BTREE
auth_tokens	PRIMARY	id	0	BTREE
auth_tokens	unique_token_hash	token_hash	0	BTREE
categories	idx_active	is_active	1	BTREE
categories	idx_slug	slug	1	BTREE
categories	idx_type	type	1	BTREE
categories	name	name	0	BTREE
categories	PRIMARY	id	0	BTREE
categories	slug	slug	0	BTREE
content	ft_search	tags	1	FULLTEXT
content	ft_search	description	1	FULLTEXT
content	ft_search	title	1	FULLTEXT
content	idx_carousel	add_to_carousel	1	BTREE
content	idx_carousel_position	carousel_position	1	BTREE
content	idx_category	category	1	BTREE
content	idx_content_quality_label	quality_label	1	BTREE
content	idx_created_at	created_at	1	BTREE
content	idx_featured	is_featured	1	BTREE
content	idx_published	is_published	1	BTREE
content	idx_section	section_id	1	BTREE
content	idx_tmdb_id	tmdb_id	1	BTREE
content	idx_type	type	1	BTREE
content	idx_year	year	1	BTREE
content	PRIMARY	id	0	BTREE
content_section_mappings	idx_content_id	content_id	1	BTREE
content_section_mappings	idx_section_id	section_id	1	BTREE
content_section_mappings	PRIMARY	id	0	BTREE
content_section_mappings	unique_content_section	section_id	0	BTREE
content_section_mappings	unique_content_section	content_id	0	BTREE
content_sections	idx_active	is_active	1	BTREE
content_sections	idx_display_order	display_order	1	BTREE
content_sections	idx_homepage	show_on_homepage	1	BTREE
content_sections	idx_slug	slug	1	BTREE
content_sections	name	name	0	BTREE
content_sections	PRIMARY	id	0	BTREE
content_sections	slug	slug	0	BTREE
episodes	idx_content_id	content_id	1	BTREE
episodes	idx_episode_number	episode_number	1	BTREE
episodes	idx_season_id	season_id	1	BTREE
episodes	PRIMARY	id	0	BTREE
episodes	unique_episode_per_season	episode_number	0	BTREE
episodes	unique_episode_per_season	season_id	0	BTREE
login_attempts	idx_created_at	created_at	1	BTREE
login_attempts	idx_ip_address	ip_address	1	BTREE
login_attempts	idx_session_id	session_id	1	BTREE
login_attempts	idx_success	success	1	BTREE
login_attempts	idx_timestamp	timestamp	1	BTREE
login_attempts	PRIMARY	id	0	BTREE
password_reset_tokens	idx_expires_at	expires_at	1	BTREE
password_reset_tokens	idx_token	token	1	BTREE
password_reset_tokens	idx_used	used	1	BTREE
password_reset_tokens	idx_user_id	user_id	1	BTREE
password_reset_tokens	PRIMARY	id	0	BTREE
password_reset_tokens	token	token	0	BTREE
seasons	idx_content_id	content_id	1	BTREE
seasons	idx_season_number	season_number	1	BTREE
seasons	PRIMARY	id	0	BTREE
seasons	unique_season_per_content	season_number	0	BTREE
seasons	unique_season_per_content	content_id	0	BTREE
section_categories	idx_category	category_id	1	BTREE
section_categories	idx_section	section_id	1	BTREE
section_categories	PRIMARY	id	0	BTREE
section_categories	unique_section_category	section_id	0	BTREE
section_categories	unique_section_category	category_id	0	BTREE
section_content_types	idx_active	is_active	1	BTREE
section_content_types	idx_section	section_id	1	BTREE
section_content_types	PRIMARY	id	0	BTREE
section_content_types	unique_section_type	type_name	0	BTREE
section_content_types	unique_section_type	section_id	0	BTREE
security_logs	idx_created_at	created_at	1	BTREE
security_logs	idx_event_type	event_type	1	BTREE
security_logs	idx_session_id	session_id	1	BTREE
security_logs	idx_severity	severity	1	BTREE
security_logs	idx_timestamp	timestamp	1	BTREE
security_logs	idx_user_id	user_id	1	BTREE
security_logs	PRIMARY	id	0	BTREE
sessions	PRIMARY	session_id	0	BTREE
user_sessions	idx_created_at	created_at	1	BTREE
user_sessions	idx_expires_at	expires_at	1	BTREE
user_sessions	idx_user_id	user_id	1	BTREE
user_sessions	PRIMARY	id	0	BTREE

=== 5. CURRENT DATA SAMPLES ===
mysql: [Warning] Using a password on the command line interface can be insecure.
data_type
CONTENT_SECTIONS
id	name	slug	description	icon	color	display_order	is_active	show_in_navigation	show_on_homepage	max_items_homepage	content_types	filter_rules	created_at	updated_at
1	Movies	movies	Latest and popular movies collection	Film	#e11d48	2	1	1	1	10	["movie"]	{"type": "movie"}	2025-07-05 20:58:58	2025-07-08 18:33:44
2	Web Series	web-series	Trending web series and TV shows	Tv	#3b82f6	3	1	1	1	10	["series"]	{"type": "series"}	2025-07-05 20:58:58	2025-07-08 18:33:48
3	Requested	requested	User requested content	Clock	#f59e0b	4	1	1	1	10	["requested"]	{"type": "requested"}	2025-07-05 20:58:58	2025-07-18 17:09:54
4	New Releases	new-releases		Folder	#10b981	1	1	1	1	10	["movie", "series"]	{}	2025-07-06 05:19:27	2025-07-18 17:09:46
6	Drama	drama		Film	#8b5cf6	4	1	1	1	10	[]	{}	2025-07-08 18:30:47	2025-07-08 18:33:57
8	Hindi Movies	hindi-movies		Folder	#ec4899	3	1	1	1	10	[]	{}	2025-07-16 17:10:43	2025-07-16 17:11:22
data_type
CONTENT_SAMPLE
id	title	type	section_id	is_published
content_1752122972013_2beif29uf	Ballerina	movie	1	1
content_1752194186530_npdnoiawm	F1	movie	4	1
content_1752412002797_lklsk98gm	The Shawshank Redemption	movie	3	1
content_1752741148958_wlg1rm0hb	Star Trek: Strange New Worlds	series	2	1
content_1752744631018_wiiu14mmt	Stranger Things	series	2	1
content_1752774122277_bloiso6z1	Superman	movie	4	1
content_1752856978763_xtivfizhe	Jurassic World Rebirth	movie	1	1
content_1752857562986_yrixbsw5r	How to Train Your Dragon	movie	4	1
content_1752857741076_a89lfue9g	Saiyaara	movie	4	1
data_type
SECTION_MAPPINGS
id	content_id	section_id	created_at
115	content_1752744631018_wiiu14mmt	2	2025-07-18 16:48:36
116	content_1752744631018_wiiu14mmt	1	2025-07-18 16:48:36
117	content_1752744631018_wiiu14mmt	4	2025-07-18 16:48:36
118	content_1752744631018_wiiu14mmt	3	2025-07-18 16:48:36
119	content_1752744631018_wiiu14mmt	6	2025-07-18 16:48:36
120	content_1752744631018_wiiu14mmt	8	2025-07-18 16:48:36
121	content_1752741148958_wlg1rm0hb	2	2025-07-18 16:48:47
122	content_1752741148958_wlg1rm0hb	4	2025-07-18 16:48:47
123	content_1752741148958_wlg1rm0hb	1	2025-07-18 16:48:47
124	content_1752741148958_wlg1rm0hb	8	2025-07-18 16:48:47
125	content_1752741148958_wlg1rm0hb	6	2025-07-18 16:48:47
126	content_1752741148958_wlg1rm0hb	3	2025-07-18 16:48:47
127	content_1752856978763_xtivfizhe	4	2025-07-18 16:48:57
128	content_1752856978763_xtivfizhe	1	2025-07-18 16:48:57
129	content_1752856978763_xtivfizhe	2	2025-07-18 16:48:57
130	content_1752856978763_xtivfizhe	8	2025-07-18 16:48:57
131	content_1752856978763_xtivfizhe	3	2025-07-18 16:48:57
132	content_1752856978763_xtivfizhe	6	2025-07-18 16:48:57
133	content_1752194186530_npdnoiawm	4	2025-07-18 16:49:11
134	content_1752194186530_npdnoiawm	1	2025-07-18 16:49:11
135	content_1752194186530_npdnoiawm	2	2025-07-18 16:49:11
136	content_1752194186530_npdnoiawm	8	2025-07-18 16:49:11
137	content_1752194186530_npdnoiawm	3	2025-07-18 16:49:11
138	content_1752194186530_npdnoiawm	6	2025-07-18 16:49:11
139	content_1752774122277_bloiso6z1	4	2025-07-18 16:49:22
140	content_1752774122277_bloiso6z1	1	2025-07-18 16:49:22
141	content_1752774122277_bloiso6z1	8	2025-07-18 16:49:22
142	content_1752774122277_bloiso6z1	2	2025-07-18 16:49:22
143	content_1752774122277_bloiso6z1	3	2025-07-18 16:49:22
144	content_1752774122277_bloiso6z1	6	2025-07-18 16:49:22
145	content_1752412002797_lklsk98gm	3	2025-07-18 16:49:32
146	content_1752412002797_lklsk98gm	4	2025-07-18 16:49:32
147	content_1752412002797_lklsk98gm	1	2025-07-18 16:49:32
148	content_1752412002797_lklsk98gm	2	2025-07-18 16:49:32
149	content_1752412002797_lklsk98gm	8	2025-07-18 16:49:32
150	content_1752412002797_lklsk98gm	6	2025-07-18 16:49:32
151	content_1752122972013_2beif29uf	1	2025-07-18 16:49:43
152	content_1752122972013_2beif29uf	4	2025-07-18 16:49:43
153	content_1752122972013_2beif29uf	8	2025-07-18 16:49:43
154	content_1752122972013_2beif29uf	6	2025-07-18 16:49:43
155	content_1752122972013_2beif29uf	2	2025-07-18 16:49:43
156	content_1752122972013_2beif29uf	3	2025-07-18 16:49:43
157	content_1752857562986_yrixbsw5r	4	2025-07-18 16:52:42
158	content_1752857562986_yrixbsw5r	8	2025-07-18 16:52:43
159	content_1752857562986_yrixbsw5r	6	2025-07-18 16:52:43
160	content_1752857562986_yrixbsw5r	3	2025-07-18 16:52:43
161	content_1752857562986_yrixbsw5r	2	2025-07-18 16:52:43
162	content_1752857562986_yrixbsw5r	1	2025-07-18 16:52:43
175	content_1752857741076_a89lfue9g	4	2025-07-18 16:55:41
176	content_1752857741076_a89lfue9g	8	2025-07-18 16:55:41
177	content_1752857741076_a89lfue9g	6	2025-07-18 16:55:41
178	content_1752857741076_a89lfue9g	1	2025-07-18 16:55:41
179	content_1752857741076_a89lfue9g	2	2025-07-18 16:55:41
180	content_1752857741076_a89lfue9g	3	2025-07-18 16:55:41
data_type
SEASONS_SAMPLE
id	content_id	web_series_title	season_number	title	description	episode_count	created_at	updated_at
season_1752741149271_72g1578bk	content_1752741148958_wlg1rm0hb	NULL	1	Season 1		4	2025-07-17 08:32:29	2025-07-19 12:52:36
season_1752744631357_x5jg7rou6	content_1752744631018_wiiu14mmt	NULL	1	Season 1		2	2025-07-17 09:30:31	2025-07-18 16:46:19
season_1752770723034_m9xvo0f6c	content_1752741148958_wlg1rm0hb	NULL	2	Season 2		1	2025-07-17 16:45:23	2025-07-17 16:45:37
season_1752771586849_6ftqaj6i7	content_1752744631018_wiiu14mmt	NULL	2	Season 2		1	2025-07-17 16:59:46	2025-07-17 17:00:01
season_1752857202550_bu9ugi3hg	content_1752744631018_wiiu14mmt	NULL	3	Season 3		1	2025-07-18 16:46:42	2025-07-18 16:46:56
data_type
EPISODES_SAMPLE
id	season_id	content_id	episode_number	title
episode_1752741149579_ek75nfmte	season_1752741149271_72g1578bk	content_1752741148958_wlg1rm0hb	1	Episode 1
episode_1752742195108_mbhagvzoe	season_1752741149271_72g1578bk	content_1752741148958_wlg1rm0hb	2	Episode 2
episode_1752744631697_gjfrap1ie	season_1752744631357_x5jg7rou6	content_1752744631018_wiiu14mmt	1	Episode 1
episode_1752770737442_onvcigjgx	season_1752770723034_m9xvo0f6c	content_1752741148958_wlg1rm0hb	1	Episode 1
episode_1752771561553_e2xlmdif7	season_1752741149271_72g1578bk	content_1752741148958_wlg1rm0hb	3	Episode 3
episode_1752771601281_lgw6r5qrl	season_1752771586849_6ftqaj6i7	content_1752744631018_wiiu14mmt	1	Episode 1
episode_1752857179186_j7lkm4d2f	season_1752744631357_x5jg7rou6	content_1752744631018_wiiu14mmt	2	Episode 2
episode_1752857216051_gx3smc0xh	season_1752857202550_bu9ugi3hg	content_1752744631018_wiiu14mmt	1	Episode 1
episode_1752929556775_im8n7rbnf	season_1752741149271_72g1578bk	content_1752741148958_wlg1rm0hb	4	Episode 4
