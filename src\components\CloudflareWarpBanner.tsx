import React from "react";

// Cloudflare WARP link
const warpLink = "https://one.one.one.one/";

export default function CloudflareWarpBanner() {
  return (
    <section
      className="mb-4 mx-auto max-w-3xl"
      style={{
        background: "none",
        boxShadow: "none",
        border: "none",
        padding: 0,
        transform: "scale(1)",
      }}
    >
      {/* Match Telegram banner structure exactly */}
      <div
        className="relative w-full mx-auto overflow-hidden"
        style={{ minHeight: 110, borderRadius: "1.25rem" }}
      >
        <div
          className="relative z-10 px-2 py-5 flex flex-col items-center justify-center"
          style={{
            minHeight: 150, // Match Telegram banner's actual content height
          }}
        >
          {/* Clickable area for the entire content */}
          <a
            href={warpLink}
            target="_blank"
            rel="noopener noreferrer"
            className="block w-full group cursor-pointer focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-background"
            style={{ borderRadius: "1.25rem" }}
            aria-label="Get Cloudflare 1.1.1.1 - The free app that makes your Internet safer"
            role="button"
            tabIndex={0}
            onKeyDown={(e) => {
              if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                window.open(warpLink, '_blank', 'noopener,noreferrer');
              }
            }}
          >
            {/* Cloudflare 1.1.1.1 Image */}
            <img
              src="/cloudflare-1111-banner.png"
              alt="Cloudflare 1.1.1.1 - The free app that makes your Internet safer"
              className="w-full transition-transform duration-200 group-hover:scale-[1.02]"
              style={{
                height: "150px", // Match content area minHeight for proper card height
                width: "100%",
                objectFit: "contain", // Show full image without cropping
                objectPosition: "center",
                display: "block",
                backgroundColor: "#ffffff", // White background to match Cloudflare branding
                borderRadius: "1.25rem", // Ensure rounded corners match container
              }}
              loading="lazy"
              onError={(e) => {
                // Fallback in case image fails to load
                const target = e.target as HTMLImageElement;
                target.style.display = 'none';
                const parent = target.parentElement;
                if (parent) {
                  parent.innerHTML = `
                    <div style="
                      background: linear-gradient(135deg, #ff6b35 0%, #e91e63 25%, #9c27b0 50%, #2196f3 75%, #00bcd4 100%);
                      color: white;
                      display: flex;
                      align-items: center;
                      justify-content: center;
                      height: 150px;
                      width: 100%;
                      border-radius: 20px;
                      font-family: system-ui, -apple-system, sans-serif;
                      font-size: 1.5rem;
                      font-weight: bold;
                    ">
                      1.1.1.1 - Get Cloudflare WARP
                    </div>
                  `;
                }
              }}
            />
          </a>
        </div>
      </div>
    </section>
  );
}
