const express = require('express');
const router = express.Router();
const { body, validationResult } = require('express-validator');
const db = require('../config/database');
const { authenticateToken, requireModerator } = require('../middleware/auth');
const { handleContentSectionMappings, getContentSections, getContentWithSections } = require('./content_sections_helper');

// Helper function to parse JSON fields with fallback for legacy comma-separated data
function parseJsonField(value) {
  if (!value) return [];

  try {
    // If it's already an array, return it
    if (Array.isArray(value)) return value;

    // Try to parse as JSON first
    const parsed = JSON.parse(value);
    return Array.isArray(parsed) ? parsed : [];
  } catch (error) {
    // If JSON parsing fails, treat as comma-separated string
    if (typeof value === 'string') {
      return value.split(',').map(item => item.trim()).filter(item => item.length > 0);
    }
    return [];
  }
}

// Enhanced function to parse genres with better error handling and debugging
function parseGenresField(value, contentId = 'unknown') {
  console.log(`[DEBUG] Parsing genres for content ${contentId}:`, { value, type: typeof value });

  if (!value) {
    console.log(`[DEBUG] No genres value for content ${contentId}`);
    return [];
  }

  try {
    // If it's already an array, return it
    if (Array.isArray(value)) {
      console.log(`[DEBUG] Genres already array for content ${contentId}:`, value);
      return value;
    }

    // Try to parse as JSON first
    const parsed = JSON.parse(value);
    if (Array.isArray(parsed)) {
      console.log(`[DEBUG] Successfully parsed JSON genres for content ${contentId}:`, parsed);
      return parsed;
    } else {
      console.log(`[DEBUG] Parsed JSON but not array for content ${contentId}:`, parsed);
      return [];
    }
  } catch (error) {
    console.log(`[DEBUG] JSON parsing failed for content ${contentId}, trying comma-separated:`, error.message);
    // If JSON parsing fails, treat as comma-separated string
    if (typeof value === 'string') {
      const result = value.split(',').map(item => item.trim()).filter(item => item.length > 0);
      console.log(`[DEBUG] Comma-separated result for content ${contentId}:`, result);
      return result;
    }
    console.log(`[DEBUG] Could not parse genres for content ${contentId}, returning empty array`);
    return [];
  }
}

// Helper function to safely convert value to JSON string
function safeJsonStringify(value) {
  if (!value) return JSON.stringify([]);
  if (Array.isArray(value)) return JSON.stringify(value);
  if (typeof value === 'string') {
    // If it's already a JSON string, validate and return
    try {
      const parsed = JSON.parse(value);
      return JSON.stringify(Array.isArray(parsed) ? parsed : []);
    } catch {
      // If not JSON, treat as comma-separated and convert
      const array = value.split(',').map(item => item.trim()).filter(item => item.length > 0);
      return JSON.stringify(array);
    }
  }
  return JSON.stringify([]);
}

// Validation middleware for content
const contentValidation = [
  body('title').trim().isLength({ min: 1, max: 255 }).withMessage('Title is required and must be 1-255 characters'),
  body('type').isIn(['movie', 'series', 'requested']).withMessage('Type must be movie, series, or requested'),
  body('description').optional().isLength({ max: 5000 }).withMessage('Description must be less than 5000 characters'),
  body('year').optional().custom((value) => {
    if (value === '' || value === null || value === undefined) return true;
    const numValue = parseInt(value);
    if (isNaN(numValue) || numValue < 1900 || numValue > 2030) {
      throw new Error('Year must be between 1900 and 2030');
    }
    return true;
  }),
  body('imdbRating').optional().custom((value) => {
    if (value === '' || value === null || value === undefined) return true;
    const numValue = parseFloat(value);
    if (isNaN(numValue) || numValue < 0 || numValue > 10) {
      throw new Error('IMDB rating must be between 0 and 10');
    }
    return true;
  }),
  body('runtime').optional().custom((value) => {
    if (value === '' || value === null || value === undefined) return true;
    const numValue = parseInt(value);
    if (isNaN(numValue) || numValue < 1) {
      throw new Error('Runtime must be a positive integer');
    }
    return true;
  }),
  // Accept both camelCase and snake_case for boolean fields
  body('isPublished').optional().isBoolean().withMessage('isPublished must be a boolean'),
  body('is_published').optional().isBoolean().withMessage('is_published must be a boolean'),
  body('isFeatured').optional().isBoolean().withMessage('isFeatured must be a boolean'),
  body('is_featured').optional().isBoolean().withMessage('is_featured must be a boolean'),
  body('addToCarousel').optional().isBoolean().withMessage('addToCarousel must be a boolean'),
  body('add_to_carousel').optional().isBoolean().withMessage('add_to_carousel must be a boolean')
];

// Test endpoint to check database connection
router.get('/test', async (req, res) => {
  try {
    const result = await db.execute('SELECT COUNT(*) as count FROM content');
    console.log('Test query result:', result);
    res.json({ success: true, result });
  } catch (error) {
    console.error('Test query error:', error);
    res.status(500).json({ success: false, error: error.message });
  }
});

// Get all content with filtering and pagination
router.get('/', async (req, res) => {
  try {
    const {
      page = 1,
      limit = 20,
      type,
      category,
      published,
      featured,
      carousel,
      search,
      sort_by = 'updated_at',
      sort_order = 'desc',
      all_ids,
      ids
    } = req.query;

    // Handle request for all content IDs (for "Select All" functionality)
    if (all_ids === 'true') {
      const query = 'SELECT id FROM content ORDER BY created_at DESC';
      const allContent = await db.execute(query);
      const allIds = allContent.map(item => item.id);
      return res.json({ success: true, data: allIds });
    }

    // Handle request for specific content by IDs
    if (ids) {
      const idArray = ids.split(',').map(id => parseInt(id)).filter(id => !isNaN(id));
      if (idArray.length > 0) {
        const placeholders = idArray.map(() => '?').join(',');
        const query = `
          SELECT c.*, cs.slug as section_slug, cs.name as section_name
          FROM content c
          LEFT JOIN content_sections cs ON c.section_id = cs.id
          WHERE c.id IN (${placeholders})
          ORDER BY c.created_at DESC
        `;
        const selectedContent = await db.execute(query, idArray);
        return res.json({ success: true, data: selectedContent });
      }
    }

    const offset = (parseInt(page) - 1) * parseInt(limit);
    const params = [];

    // Build the main query with section information
    let query = `
      SELECT c.*, cs.slug as section_slug, cs.name as section_name
      FROM content c
      LEFT JOIN content_sections cs ON c.section_id = cs.id
      WHERE 1=1
    `;

    // Add filters
    if (type) {
      query += ' AND c.type = ?';
      params.push(type);
    }

    if (category) {
      query += ' AND c.category = ?';
      params.push(category);
    }

    if (published !== undefined) {
      query += ' AND c.is_published = ?';
      params.push(published === 'true' ? 1 : 0);
    }

    if (featured !== undefined) {
      query += ' AND c.is_featured = ?';
      params.push(featured === 'true' ? 1 : 0);
    }

    if (carousel !== undefined) {
      query += ' AND c.add_to_carousel = ?';
      params.push(carousel === 'true' ? 1 : 0);
      
      // If filtering for carousel items, add proper ordering by carousel_position
      if (carousel === 'true') {
        console.log('[Content API] Filtering for carousel items with proper ordering');
      }
    }

    if (search) {
      query += ' AND (c.title LIKE ? OR c.description LIKE ? OR c.tags LIKE ?)';
      const searchTerm = `%${search}%`;
      params.push(searchTerm, searchTerm, searchTerm);
    }

    // Enhanced sorting with priority for recently updated series
    const allowedSortFields = ['title', 'year', 'created_at', 'updated_at', 'imdb_rating'];
    const sortField = allowedSortFields.includes(sort_by) ? sort_by : 'created_at';
    const sortDirection = sort_order.toLowerCase() === 'asc' ? 'ASC' : 'DESC';
    
    // Special sorting for carousel items - prioritize carousel_position
    if (carousel === 'true') {
      query += ` ORDER BY 
        CASE 
          WHEN c.carousel_position IS NULL OR c.carousel_position = 0 THEN 999999
          ELSE c.carousel_position 
        END ASC,
        c.created_at DESC`;
    } else {
      // Use consistent chronological ordering for all content types in Manage Content
      query += ` ORDER BY c.${sortField} ${sortDirection}`;
    }
    
    query += ` LIMIT ${parseInt(limit)} OFFSET ${offset}`;

    console.log('Executing query:', query);
    console.log('With params:', params);

    let result;
    try {
      result = await db.execute(query, params);
      console.log('Raw result:', result);
    } catch (error) {
      console.error('Database query error:', error);
      return res.status(500).json({
        success: false,
        message: 'Database query failed',
        error: error.message,
        data: []
      });
    }
    console.log('Result type:', typeof result, 'Array?', Array.isArray(result));
    console.log('Result[0] type:', typeof result[0], 'Array?', Array.isArray(result[0]));

    // Handle different possible result structures
    // db.execute returns rows directly, not [rows, fields]
    const contentRows = result || [];
    console.log('Content rows received:', contentRows ? contentRows.length : 0);

    // Get sections for each content item
    for (let item of contentRows) {
      if (item && item.id) {
        item.sections = await getContentSections(item.id);
        item.section_ids = item.sections.map(s => s.id);
      }
    }

    // Get total count for pagination
    let countQuery = `SELECT COUNT(*) as total FROM content c WHERE 1=1`;
    const countParams = [];
    
    if (type) {
      countQuery += ' AND c.type = ?';
      countParams.push(type);
    }

    if (category) {
      countQuery += ' AND c.category = ?';
      countParams.push(category);
    }

    if (published !== undefined) {
      countQuery += ' AND c.is_published = ?';
      countParams.push(published === 'true' ? 1 : 0);
    }

    if (featured !== undefined) {
      countQuery += ' AND c.is_featured = ?';
      countParams.push(featured === 'true' ? 1 : 0);
    }

    if (search) {
      countQuery += ' AND (c.title LIKE ? OR c.description LIKE ? OR c.tags LIKE ?)';
      const searchTerm = `%${search}%`;
      countParams.push(searchTerm, searchTerm, searchTerm);
    }

    const countResult = await db.execute(countQuery, countParams);
    // db.execute returns rows directly, not [rows, fields]
    const countRows = countResult || [];
    const total = countRows[0]?.total || countRows[0]?.count || 0;

    // Safely map database fields to frontend expected field names
    const mappedContent = (contentRows || []).filter(item => item && item.id).map(item => {
      try {
        return {
          ...item,
          // Map boolean fields from database (0/1) to frontend (true/false)
          isPublished: Boolean(item.is_published),
          isFeatured: Boolean(item.is_featured),
          addToCarousel: Boolean(item.add_to_carousel),
          // Map image fields for frontend compatibility
          image: item.poster_url || item.thumbnail_url || '',
          coverImage: item.poster_url || item.thumbnail_url || '',
          posterUrl: item.poster_url || '',
          thumbnailUrl: item.thumbnail_url || '',
          // Map other fields
          tmdbId: item.tmdb_id || '',
          imdbRating: item.imdb_rating || null,
          subtitleUrl: item.subtitle_url || '',
          trailer: item.trailer || '',
          secureVideoLinks: item.secure_video_links || '',
          totalSeasons: item.total_seasons || (item.type === 'series' ? 1 : 0),
          totalEpisodes: item.total_episodes || 0,
          createdAt: item.created_at,
          updatedAt: item.updated_at,
          // Map section fields
          section: item.section_slug || '',
          sectionName: item.section_name || '',
          sectionId: item.section_id || null,
          // Multiple sections support
          sections: item.sections || [],
          section_ids: item.section_ids || [],
          // Parse JSON fields with enhanced error handling and debugging
          genres: parseGenresField(item.genres, item.id),
          languages: parseJsonField(item.languages),
          quality: parseJsonField(item.quality),
          qualityLabel: item.quality_label || null,
          audioTracks: parseJsonField(item.audio_tracks),
        };
      } catch (error) {
        console.error('Error mapping content item:', error, item);
        return null;
      }
    }).filter(Boolean);

    res.json({
      success: true,
      data: mappedContent,
      total,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        totalPages: Math.ceil(total / parseInt(limit)),
        hasNext: parseInt(page) < Math.ceil(total / parseInt(limit)),
        hasPrev: parseInt(page) > 1
      }
    });

  } catch (error) {
    console.error('Error fetching content:', error);
    res.status(500).json({
      success: false,
      error: 'Internal Server Error',
      message: 'Failed to fetch content'
    });
  }
});

// Get single content item by ID
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;


    const query = `
      SELECT c.*, cs.slug as section_slug, cs.name as section_name
      FROM content c
      LEFT JOIN content_sections cs ON c.section_id = cs.id
      WHERE c.id = ?
    `;

    const result = await db.execute(query, [id]);
    // db.execute returns rows directly, not [rows, fields]
    const contentRows = result || [];

    if (!contentRows || contentRows.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'Not Found',
        message: 'Content not found'
      });
    }

    const item = contentRows[0];

    // Get sections for this content item
    item.sections = await getContentSections(item.id);
    item.section_ids = item.sections.map(s => s.id);

    // Safely map database fields to frontend expected field names
    const mappedItem = {
      ...item,
      // Map boolean fields from database (0/1) to frontend (true/false)
      isPublished: Boolean(item.is_published),
      isFeatured: Boolean(item.is_featured),
      addToCarousel: Boolean(item.add_to_carousel),
      // Map image fields for frontend compatibility
      image: item.poster_url || item.thumbnail_url || '',
      coverImage: item.poster_url || item.thumbnail_url || '',
      posterUrl: item.poster_url || '',
      thumbnailUrl: item.thumbnail_url || '',
      // Map other fields
      tmdbId: item.tmdb_id || '',
      imdbRating: item.imdb_rating || null,
      subtitleUrl: item.subtitle_url || '',
      trailer: item.trailer || '',
      secureVideoLinks: item.secure_video_links || '',
      totalSeasons: item.total_seasons || (item.type === 'series' ? 1 : 0),
      totalEpisodes: item.total_episodes || 0,
      createdAt: item.created_at,
      updatedAt: item.updated_at,
      // Map section fields
      section: item.section_slug || '',
      sectionName: item.section_name || '',
      sectionId: item.section_id || null,
      // Multiple sections support
      sections: item.sections || [],
      section_ids: item.section_ids || [],
      // Parse JSON fields with enhanced error handling and debugging
      genres: parseGenresField(item.genres, item.id),
      languages: parseJsonField(item.languages),
      quality: parseJsonField(item.quality),
      qualityLabel: item.quality_label || null,
      audioTracks: parseJsonField(item.audio_tracks),
    };

    res.json({
      success: true,
      data: mappedItem
    });

  } catch (error) {
    console.error('Error fetching content by ID:', error);
    res.status(500).json({
      success: false,
      error: 'Internal Server Error',
      message: 'Failed to fetch content'
    });
  }
});

// Create new content (admin/moderator only)
router.post('/', authenticateToken, requireModerator, contentValidation, async (req, res) => {
  console.log('POST /content - Request received:', {
    userId: req.user?.id,
    userRole: req.user?.role,
    userActive: req.user?.is_active,
    bodyKeys: Object.keys(req.body || {}),
    authHeader: req.headers.authorization ? 'Present' : 'Missing'
  });
  
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: 'Validation Error',
        details: errors.array()
      });
    }

    const {
      title,
      type,
      category,
      section,
      tmdbId,
      year,
      genres = [],
      languages = [],
      description,
      posterUrl,
      thumbnailUrl,
      videoLinks,
      secureVideoLinks,
      quality = [],
      qualityLabel,
      tags,
      imdbRating,
      runtime,
      studio,
      audioTracks = [],
      trailer,
      subtitleUrl,
      // Handle both camelCase and snake_case for boolean fields
      isPublished,
      is_published,
      isFeatured,
      is_featured,
      addToCarousel,
      add_to_carousel,
      // Season and episode counts for web series
      totalSeasons,
      totalEpisodes,
      // Multiple sections support
      section_ids
    } = req.body;

    // Normalize boolean fields (prefer camelCase from frontend, fallback to snake_case)
    const normalizedIsPublished = isPublished !== undefined ? isPublished : (is_published !== undefined ? is_published : false);
    const normalizedIsFeatured = isFeatured !== undefined ? isFeatured : (is_featured !== undefined ? is_featured : false);
    const normalizedAddToCarousel = addToCarousel !== undefined ? addToCarousel : (add_to_carousel !== undefined ? add_to_carousel : false);

    // Convert arrays to JSON strings for database storage using safe function
    const genresJson = safeJsonStringify(genres);
    const languagesJson = safeJsonStringify(languages);
    const qualityJson = safeJsonStringify(quality);
    const audioTracksJson = safeJsonStringify(audioTracks);

    // Generate unique ID for content
    const contentId = `content_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    // Debug logging for quality data
    console.log('Quality data processing (CREATE):', {
      originalQuality: quality,
      qualityType: typeof quality,
      qualityArray: Array.isArray(quality),
      qualityJson: qualityJson,
      contentId: contentId
    });

    // Convert section slug to section_id
    let sectionId = null;
    if (section) {
      const sectionResult = await db.execute('SELECT id FROM content_sections WHERE slug = ? OR id = ?', [section, section]);
      let sectionRows = [];
      if (Array.isArray(sectionResult)) {
        if (Array.isArray(sectionResult[0])) {
          sectionRows = sectionResult[0];
        } else {
          sectionRows = sectionResult;
        }
      }
      if (sectionRows.length > 0) {
        sectionId = sectionRows[0].id;
      }
    }

    // Since quality_label column exists, use it directly
    const query = `
      INSERT INTO content (
        id, title, type, category, section_id, tmdb_id, year, genres, languages,
        description, poster_url, thumbnail_url, secure_video_links,
        quality, quality_label, tags, imdb_rating, runtime, studio, audio_tracks, trailer,
        subtitle_url, is_published, is_featured, add_to_carousel,
        total_seasons, total_episodes, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
    `;
    
    const values = [
      contentId,
      title,
      type,
      category,
      sectionId,
      tmdbId || null,
      year || null,
      genresJson,
      languagesJson,
      description || null,
      posterUrl || null,
      thumbnailUrl || null,
      secureVideoLinks || videoLinks || null,
      qualityJson,
      qualityLabel || null,
      tags || null,
      imdbRating || null,
      runtime || null,
      studio || null,
      audioTracksJson,
      trailer || null,
      subtitleUrl || null,
      normalizedIsPublished ? 1 : 0,
      normalizedIsFeatured ? 1 : 0,
      normalizedAddToCarousel ? 1 : 0,
      totalSeasons || 0,
      totalEpisodes || 0
    ];

    // Debug logging
    console.log('=== INSERT QUERY DEBUG ===');
    console.log('Query placeholders count:', (query.match(/\?/g) || []).length);
    console.log('Values array length:', values.length);
    console.log('Content ID:', contentId);
    
    if ((query.match(/\?/g) || []).length !== values.length) {
      console.error('MISMATCH: Placeholders vs Values');
      console.error('Placeholders:', (query.match(/\?/g) || []).length);
      console.error('Values:', values.length);
      throw new Error('Column count mismatch detected before execution');
    }
    
    await db.execute(query, values);

    // Always auto-assign based on type, then add any manually selected sections
    console.log(`[SECTION AUTO-ASSIGN] Processing content: ${contentId}, type: ${type}`);
    let finalSectionIds = [];
    
    // Auto-assign based on content type
    if (type === 'movie') {
      // Movies go to "Movies" section (section_id: 1)
      finalSectionIds.push(1);
      console.log(`[SECTION AUTO-ASSIGN] Movie detected - adding to Movies section (ID: 1)`);
    } else if (type === 'series') {
      // Web Series go to "Web Series" section (section_id: 2)
      finalSectionIds.push(2);
      console.log(`[SECTION AUTO-ASSIGN] Web Series detected - adding to Web Series section (ID: 2)`);
    }
    
    // Add manually selected sections if provided
    if (section_ids && Array.isArray(section_ids) && section_ids.length > 0) {
      finalSectionIds.push(...section_ids);
      console.log(`[SECTION AUTO-ASSIGN] Manual sections added: ${section_ids}`);
    }
    
    // If we have a primary section_id from the legacy section field, include it too
    if (sectionId) {
      finalSectionIds.push(sectionId);
      console.log(`[SECTION AUTO-ASSIGN] Legacy section_id found: ${sectionId}`);
    }
    
    // Remove duplicates and assign to sections
    const uniqueSectionIds = [...new Set(finalSectionIds)];
    console.log(`[SECTION AUTO-ASSIGN] Final section IDs to assign: ${uniqueSectionIds}`);
    
    if (uniqueSectionIds.length > 0) {
      console.log(`[SECTION AUTO-ASSIGN] Calling handleContentSectionMappings for content: ${contentId}`);
      await handleContentSectionMappings(contentId, uniqueSectionIds);
      console.log(`[SECTION AUTO-ASSIGN] Section assignment completed for content: ${contentId}`);
    } else {
      console.log(`[SECTION AUTO-ASSIGN] No sections to assign for content: ${contentId}`);
    }

    res.status(201).json({
      success: true,
      message: 'Content created successfully',
      data: {
        id: contentId,
        title,
        type,
        category
      }
    });

  } catch (error) {
    console.error('Error creating content:', error);
    res.status(500).json({
      success: false,
      error: 'Internal Server Error',
      message: 'Failed to create content'
    });
  }
});

// Update content (admin/moderator only)
router.put('/:id', authenticateToken, requireModerator, contentValidation, async (req, res) => {
  console.log('PUT /content/:id - Request received:', {
    contentId: req.params.id,
    userId: req.user?.id,
    userRole: req.user?.role,
    userActive: req.user?.is_active,
    bodyKeys: Object.keys(req.body || {}),
    authHeader: req.headers.authorization ? 'Present' : 'Missing'
  });
  
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: 'Validation Error',
        details: errors.array()
      });
    }

    const { id } = req.params;
    const {
      title,
      type,
      category,
      section,
      tmdbId,
      year,
      genres = [],
      languages = [],
      description,
      posterUrl,
      thumbnailUrl,
      videoLinks,
      secureVideoLinks,
      quality = [],
      qualityLabel,
      tags,
      imdbRating,
      runtime,
      studio,
      audioTracks = [],
      trailer,
      subtitleUrl,
      // Handle both camelCase and snake_case for boolean fields
      isPublished,
      is_published,
      isFeatured,
      is_featured,
      addToCarousel,
      add_to_carousel,
      // Season and episode counts for web series
      totalSeasons,
      totalEpisodes,
      // Multiple sections support
      section_ids
    } = req.body;

    // Check if content exists and get current values
    const existingResult = await db.execute('SELECT id, is_published, is_featured, add_to_carousel, section_id, genres, languages, quality, quality_label, audio_tracks FROM content WHERE id = ?', [id]);
    let existingContent = [];
    if (Array.isArray(existingResult)) {
      existingContent = existingResult;
    }
    if (!existingContent || existingContent.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'Not Found',
        message: 'Content not found'
      });
    }

    const currentContent = existingContent[0];

    // Normalize boolean fields (prefer camelCase from frontend, fallback to snake_case, then preserve existing values)
    const normalizedIsPublished = isPublished !== undefined ? isPublished :
                                  (is_published !== undefined ? is_published :
                                  Boolean(currentContent.is_published));
    const normalizedIsFeatured = isFeatured !== undefined ? isFeatured :
                                (is_featured !== undefined ? is_featured :
                                Boolean(currentContent.is_featured));
    const normalizedAddToCarousel = addToCarousel !== undefined ? addToCarousel :
                                   (add_to_carousel !== undefined ? add_to_carousel :
                                   Boolean(currentContent.add_to_carousel));

    // Preserve existing array data if not provided in update
    const finalGenres = genres && genres.length > 0 ? genres : parseJsonField(currentContent.genres);
    const finalLanguages = languages && languages.length > 0 ? languages : parseJsonField(currentContent.languages);
    const finalQuality = quality && quality.length > 0 ? quality : parseJsonField(currentContent.quality);
    const finalAudioTracks = audioTracks && audioTracks.length > 0 ? audioTracks : parseJsonField(currentContent.audio_tracks);
    
    // Preserve existing quality_label if not provided in update
    const finalQualityLabel = qualityLabel !== undefined ? qualityLabel : currentContent.quality_label;


    // Hero Carousel validation for updates: Check if adding to carousel would exceed limit
    if (normalizedAddToCarousel && (!currentContent || !currentContent.add_to_carousel)) {
      try {
        const carouselCountResult = await db.execute(
          'SELECT COUNT(*) as count FROM content WHERE add_to_carousel = 1 AND is_published = 1 AND id != ?',
          [id]
        );
        
        let carouselCount = 0;
        if (Array.isArray(carouselCountResult)) {
          if (Array.isArray(carouselCountResult[0])) {
            carouselCount = carouselCountResult[0][0]?.count || 0;
          } else {
            carouselCount = carouselCountResult[0]?.count || 0;
          }
        }
        
        // Check if carousel is at maximum capacity (10 items)
        if (carouselCount >= 10) {
          return res.status(400).json({
            success: false,
            error: 'Carousel Limit Exceeded',
            message: 'Hero carousel is at maximum capacity (10 items). Please remove an existing item from the carousel before adding a new one.',
            details: {
              currentCount: carouselCount,
              maxAllowed: 10,
              suggestion: 'Go to Manage Content and uncheck "Add to Carousel" for an existing item first.'
            }
          });
        }
      } catch (carouselError) {
        console.error('Error checking carousel count:', carouselError);
        // Continue with update but log the error
      }
    }

    // Convert arrays to JSON strings for database storage using safe function
    const genresJson = safeJsonStringify(finalGenres);
    const languagesJson = safeJsonStringify(finalLanguages);
    const qualityJson = safeJsonStringify(finalQuality);
    const audioTracksJson = safeJsonStringify(finalAudioTracks);

    // Debug logging for quality data update
    console.log('Quality data processing (UPDATE):', {
      originalQuality: quality,
      finalQuality: finalQuality,
      qualityType: typeof finalQuality,
      qualityArray: Array.isArray(finalQuality),
      qualityJson: qualityJson,
      originalQualityLabel: qualityLabel,
      finalQualityLabel: finalQualityLabel,
      existingQualityLabel: currentContent.quality_label,
      contentId: id
    });

    // Convert section slug to section_id, preserve existing if not provided
    let sectionId = currentContent.section_id; // Preserve existing section_id by default
    if (section) {
      const sectionResult = await db.execute('SELECT id FROM content_sections WHERE slug = ? OR id = ?', [section, section]);
      let sectionRows = [];
      if (Array.isArray(sectionResult)) {
        sectionRows = sectionResult;
      }
      if (sectionRows.length > 0) {
        sectionId = sectionRows[0].id;
      }
    }

    // Since quality_label column exists, use it directly
    const query = `
      UPDATE content SET
        title = ?, type = ?, category = ?, section_id = ?, tmdb_id = ?, year = ?,
        genres = ?, languages = ?, description = ?, poster_url = ?, thumbnail_url = ?,
        secure_video_links = ?, quality = ?, quality_label = ?, tags = ?, imdb_rating = ?,
        runtime = ?, studio = ?, audio_tracks = ?, trailer = ?, subtitle_url = ?,
        is_published = ?, is_featured = ?, add_to_carousel = ?,
        total_seasons = ?, total_episodes = ?, updated_at = NOW()
      WHERE id = ?
    `;
    
    const values = [
      title,
      type,
      category,
      sectionId,
      tmdbId || null,
      year || null,
      genresJson,
      languagesJson,
      description || null,
      posterUrl || null,
      thumbnailUrl || null,
      secureVideoLinks || videoLinks || null,
      qualityJson,
      finalQualityLabel,
      tags || null,
      imdbRating || null,
      runtime || null,
      studio || null,
      audioTracksJson,
      trailer || null,
      subtitleUrl || null,
      normalizedIsPublished ? 1 : 0,
      normalizedIsFeatured ? 1 : 0,
      normalizedAddToCarousel ? 1 : 0,
      totalSeasons || 0,
      totalEpisodes || 0,
      id
    ];

    // Debug logging for UPDATE
    console.log('=== UPDATE QUERY DEBUG ===');
    console.log('Query placeholders count:', (query.match(/\?/g) || []).length);
    console.log('Values array length:', values.length);
    console.log('Content ID for update:', id);
    
    if ((query.match(/\?/g) || []).length !== values.length) {
      console.error('UPDATE MISMATCH: Placeholders vs Values');
      console.error('Placeholders:', (query.match(/\?/g) || []).length);
      console.error('Values:', values.length);
      throw new Error('UPDATE column count mismatch detected before execution');
    }
    
    await db.execute(query, values);

    // Always auto-assign based on type, then add any manually selected sections
    console.log(`[SECTION AUTO-ASSIGN UPDATE] Processing content: ${id}, type: ${type}`);
    let finalSectionIds = [];
    
    // Auto-assign based on content type
    if (type === 'movie') {
      // Movies go to "Movies" section (section_id: 1)
      finalSectionIds.push(1);
      console.log(`[SECTION AUTO-ASSIGN UPDATE] Movie detected - adding to Movies section (ID: 1)`);
    } else if (type === 'series') {
      // Web Series go to "Web Series" section (section_id: 2)
      finalSectionIds.push(2);
      console.log(`[SECTION AUTO-ASSIGN UPDATE] Web Series detected - adding to Web Series section (ID: 2)`);
    }
    
    // Add manually selected sections if provided
    if (section_ids && Array.isArray(section_ids) && section_ids.length > 0) {
      finalSectionIds.push(...section_ids);
      console.log(`[SECTION AUTO-ASSIGN UPDATE] Manual sections added: ${section_ids}`);
    }
    
    // If we have a primary section_id from the legacy section field, include it too
    if (sectionId) {
      finalSectionIds.push(sectionId);
      console.log(`[SECTION AUTO-ASSIGN UPDATE] Legacy section_id found: ${sectionId}`);
    }
    
    // Remove duplicates and assign to sections
    const uniqueSectionIds = [...new Set(finalSectionIds)];
    console.log(`[SECTION AUTO-ASSIGN UPDATE] Final section IDs to assign: ${uniqueSectionIds}`);
    
    if (uniqueSectionIds.length > 0) {
      console.log(`[SECTION AUTO-ASSIGN UPDATE] Calling handleContentSectionMappings for content: ${id}`);
      await handleContentSectionMappings(id, uniqueSectionIds);
      console.log(`[SECTION AUTO-ASSIGN UPDATE] Section assignment completed for content: ${id}`);
    } else {
      console.log(`[SECTION AUTO-ASSIGN UPDATE] No sections to assign for content: ${id}`);
    }

    res.json({
      success: true,
      message: 'Content updated successfully',
      data: {
        id,
        title,
        type,
        category
      }
    });

  } catch (error) {
    console.error('Error updating content - detailed:', {
      error: error.message,
      stack: error.stack,
      contentId: req.params.id,
      userId: req.user?.id,
      sqlState: error.sqlState,
      code: error.code
    });
    
    // Handle specific database errors
    if (error.code === 'ER_NO_SUCH_TABLE') {
      return res.status(500).json({
        success: false,
        error: 'Database Error',
        message: 'Content table not found',
        code: 'TABLE_NOT_FOUND'
      });
    }
    
    if (error.code === 'ER_ACCESS_DENIED_ERROR') {
      return res.status(500).json({
        success: false,
        error: 'Database Error',
        message: 'Database access denied',
        code: 'DB_ACCESS_DENIED'
      });
    }
    
    res.status(500).json({
      success: false,
      error: 'Internal Server Error',
      message: 'Failed to update content',
      code: 'UPDATE_ERROR'
    });
  }
});

// Bulk create content (admin/moderator only)
router.post('/bulk-create', authenticateToken, requireModerator, async (req, res) => {
  try {
    const { items } = req.body;

    if (!Array.isArray(items) || items.length === 0) {
      return res.status(400).json({
        success: false,
        error: 'Validation Error',
        message: 'Items array is required and cannot be empty'
      });
    }

    let created = 0;
    let failed = 0;
    const errors = [];

    for (const item of items) {
      try {
        // Convert section slug to section_id
        let sectionId = null;
        if (item.section) {
          const sectionResult = await db.execute('SELECT id FROM content_sections WHERE slug = ? OR id = ?', [item.section, item.section]);
          let sectionRows = [];
          if (Array.isArray(sectionResult)) {
            if (Array.isArray(sectionResult[0])) {
              sectionRows = sectionResult[0];
            } else {
              sectionRows = sectionResult;
            }
          }
          if (sectionRows.length > 0) {
            sectionId = sectionRows[0].id;
          }
        }

        // Convert arrays to JSON strings for database storage using safe function
        const genresJson = safeJsonStringify(item.genres);
        const languagesJson = safeJsonStringify(item.languages);
        const qualityJson = safeJsonStringify(item.quality);
        const audioTracksJson = safeJsonStringify(item.audioTracks);

        // Generate unique ID for content
        const contentId = `content_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

        const query = `
          INSERT INTO content (
            id, title, type, category, section_id, tmdb_id, year, genres, languages,
            description, poster_url, thumbnail_url, secure_video_links, quality, quality_label, tags,
            imdb_rating, runtime, studio, audio_tracks, trailer, subtitle_url,
            is_published, is_featured, add_to_carousel, created_at, updated_at
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
        `;

        await db.execute(query, [
          contentId,
          item.title,
          item.type,
          item.category,
          sectionId,
          item.tmdbId || null,
          item.year || null,
          genresJson,
          languagesJson,
          item.description || null,
          item.posterUrl || null,
          item.thumbnailUrl || null,
          item.secureVideoLinks || item.videoLinks || null,
          qualityJson,
          null,
          item.tags || null,
          item.imdbRating || null,
          item.runtime || null,
          item.studio || null,
          audioTracksJson,
          item.trailer || null,
          item.subtitleUrl || null,
          item.isPublished ? 1 : 0,
          item.isFeatured ? 1 : 0,
          item.addToCarousel ? 1 : 0
        ]);

        created++;
      } catch (error) {
        console.error(`Error creating bulk item ${item.title}:`, error);
        failed++;
        errors.push({
          title: item.title,
          error: error.message
        });
      }
    }

    res.status(201).json({
      success: true,
      message: `Bulk create completed. ${created} items created, ${failed} items failed.`,
      data: {
        created,
        failed,
        total: items.length,
        errors
      }
    });

  } catch (error) {
    console.error('Error in bulk create:', error);
    res.status(500).json({
      success: false,
      error: 'Internal Server Error',
      message: 'Failed to bulk create content'
    });
  }
});

// Bulk delete content (admin/moderator only)
router.post('/bulk', authenticateToken, requireModerator, async (req, res) => {
  try {
    const { action, contentIds } = req.body;
    
    console.log('Bulk operation request:', { action, contentIds });

    if (action !== 'delete') {
      return res.status(400).json({
        success: false,
        error: 'Bad Request',
        message: 'Only delete action is supported'
      });
    }

    if (!contentIds || !Array.isArray(contentIds) || contentIds.length === 0) {
      return res.status(400).json({
        success: false,
        error: 'Bad Request',
        message: 'Content IDs array is required'
      });
    }

    console.log(`Attempting to delete ${contentIds.length} content items`);

    const deletedItems = [];
    const failedItems = [];

    // Process each content ID
    for (const contentId of contentIds) {
      try {
        console.log(`Deleting content: ${contentId}`);

        // Check if content exists
        const existingResult = await db.execute('SELECT id, title, type FROM content WHERE id = ?', [contentId]);
        let existingContent = [];
        if (Array.isArray(existingResult)) {
          if (Array.isArray(existingResult[0])) {
            existingContent = existingResult[0];
          } else {
            existingContent = existingResult;
          }
        }

        if (!existingContent || existingContent.length === 0) {
          console.log(`Content not found: ${contentId}`);
          failedItems.push({
            id: contentId,
            error: 'Content not found'
          });
          continue;
        }

        const content = existingContent[0];
        console.log(`Found content: ${content.title} (${content.type})`);

        // For web series, delete all related seasons and episodes first
        if (content.type === 'series') {
          console.log(`Deleting seasons and episodes for series: ${contentId}`);
          
          // Delete all episodes for this content
          await db.execute('DELETE FROM episodes WHERE content_id = ?', [contentId]);
          console.log(`Deleted episodes for content: ${contentId}`);
          
          // Delete all seasons for this content
          await db.execute('DELETE FROM seasons WHERE content_id = ?', [contentId]);
          console.log(`Deleted seasons for content: ${contentId}`);
        }

        // Delete the main content
        await db.execute('DELETE FROM content WHERE id = ?', [contentId]);
        console.log(`Deleted content: ${contentId}`);

        deletedItems.push({
          id: contentId,
          title: content.title,
          type: content.type
        });

      } catch (itemError) {
        console.error(`Error deleting content ${contentId}:`, itemError);
        failedItems.push({
          id: contentId,
          error: itemError.message
        });
      }
    }

    console.log(`Bulk delete completed. Deleted: ${deletedItems.length}, Failed: ${failedItems.length}`);

    // Return results
    const response = {
      success: true,
      message: `Bulk delete completed. ${deletedItems.length} items deleted, ${failedItems.length} failed.`,
      results: {
        deleted: deletedItems,
        failed: failedItems,
        totalRequested: contentIds.length,
        totalDeleted: deletedItems.length,
        totalFailed: failedItems.length
      }
    };

    // If some items failed, include that in the response
    if (failedItems.length > 0) {
      response.warning = `${failedItems.length} items could not be deleted`;
    }

    res.json(response);

  } catch (error) {
    console.error('Error in bulk delete:', error);
    res.status(500).json({
      success: false,
      error: 'Internal Server Error',
      message: 'Failed to perform bulk delete operation',
      details: error.message
    });
  }
});

// Delete single content (admin/moderator only)
router.delete('/:id', authenticateToken, requireModerator, async (req, res) => {
  try {
    const { id } = req.params;

    console.log(`Deleting single content: ${id}`);

    // Check if content exists
    const existingResult = await db.execute('SELECT id, title, type FROM content WHERE id = ?', [id]);
    let existingContent = [];
    if (Array.isArray(existingResult)) {
      if (Array.isArray(existingResult[0])) {
        existingContent = existingResult[0];
      } else {
        existingContent = existingResult;
      }
    }
    if (!existingContent || existingContent.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'Not Found',
        message: 'Content not found'
      });
    }

    const content = existingContent[0];
    console.log(`Found content: ${content.title} (${content.type})`);

    // For web series, delete all related seasons and episodes first
    if (content.type === 'series') {
      console.log(`Deleting seasons and episodes for series: ${id}`);
      
      // Delete all episodes for this content
      await db.execute('DELETE FROM episodes WHERE content_id = ?', [id]);
      console.log(`Deleted episodes for content: ${id}`);
      
      // Delete all seasons for this content
      await db.execute('DELETE FROM seasons WHERE content_id = ?', [id]);
      console.log(`Deleted seasons for content: ${id}`);
    }

    // Delete the main content
    await db.execute('DELETE FROM content WHERE id = ?', [id]);
    console.log(`Deleted content: ${id}`);

    res.json({
      success: true,
      message: 'Content deleted successfully',
      deletedContent: {
        id: content.id,
        title: content.title,
        type: content.type
      }
    });

  } catch (error) {
    console.error('Error deleting content:', error);
    res.status(500).json({
      success: false,
      error: 'Internal Server Error',
      message: 'Failed to delete content'
    });
  }
});

module.exports = router;
