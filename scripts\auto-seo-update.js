#!/usr/bin/env node

/**
 * Automatic SEO Update Script for StreamDB
 * Runs daily to update sitemap and notify search engines
 * 
 * Features:
 * - Generates fresh sitemap with current content
 * - Notifies Google and Bing about updates
 * - Logs activity for monitoring
 * - Can be scheduled with cron
 */

import fs from 'fs';
import path from 'path';
import https from 'https';
import { fileURLToPath } from 'url';
import { generateSitemap } from './generate-sitemap.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Configuration
const siteUrl = 'https://streamdb.online';
const logDir = path.join(__dirname, '..', 'logs');
const logFile = path.join(logDir, 'seo-updates.log');

// Ensure log directory exists
if (!fs.existsSync(logDir)) {
  fs.mkdirSync(logDir, { recursive: true });
}

/**
 * Log message to file and console
 */
function log(message) {
  const timestamp = new Date().toISOString();
  const logMessage = `[${timestamp}] ${message}\n`;
  
  console.log(message);
  
  fs.appendFileSync(logFile, logMessage);
}

/**
 * Notify search engines about sitemap update with retry logic
 */
async function notifySearchEngines() {
  log('🔔 Notifying search engines about sitemap update...');

  // First verify sitemap is accessible
  try {
    const sitemapUrl = `${siteUrl}/sitemap.xml`;
    log(`🔍 Verifying sitemap accessibility: ${sitemapUrl}`);

    await new Promise((resolve) => {
      const req = https.get(sitemapUrl, (res) => {
        if (res.statusCode === 200) {
          log(`✅ Sitemap is accessible (${res.statusCode})`);
          resolve();
        } else {
          log(`⚠️ Sitemap returned status ${res.statusCode} - continuing anyway`);
          resolve(); // Continue even if sitemap check fails
        }
      });

      req.on('error', (err) => {
        log(`⚠️ Could not verify sitemap accessibility: ${err.message} - continuing anyway`);
        resolve(); // Continue even if verification fails
      });

      req.setTimeout(5000, () => {
        req.destroy();
        log(`⚠️ Sitemap verification timeout - continuing anyway`);
        resolve();
      });
    });
  } catch (error) {
    log(`⚠️ Sitemap verification failed: ${error.message} - continuing anyway`);
  }

  // Note: Google ping service is deprecated as of 2023
  // Modern approach: Submit sitemap via Google Search Console API or manual submission
  const searchEngines = [
    {
      name: 'Google (Legacy - Deprecated)',
      url: `https://www.google.com/ping?sitemap=${encodeURIComponent(siteUrl + '/sitemap.xml')}`,
      deprecated: true,
      modernApproach: 'Use Google Search Console to submit sitemap manually'
    },
    {
      name: 'Bing',
      url: `https://www.bing.com/ping?sitemap=${encodeURIComponent(siteUrl + '/sitemap.xml')}`,
      deprecated: false
    }
  ];

  for (const engine of searchEngines) {
    if (engine.deprecated) {
      log(`⚠️ ${engine.name} service is deprecated`);
      log(`💡 Modern approach: ${engine.modernApproach}`);
      log(`📝 Sitemap URL for manual submission: ${siteUrl}/sitemap.xml`);
      continue;
    }

    let attempts = 0;
    const maxAttempts = 2;

    while (attempts < maxAttempts) {
      attempts++;
      try {
        const success = await new Promise((resolve) => {
          const req = https.get(engine.url, (res) => {
            let data = '';
            res.on('data', chunk => data += chunk);
            res.on('end', () => {
              if (res.statusCode === 200) {
                log(`✅ Successfully notified ${engine.name} (attempt ${attempts})`);
                resolve(true);
              } else if (res.statusCode === 410) {
                log(`⚠️ ${engine.name} service permanently unavailable (410)`);
                log(`💡 Submit sitemap manually at Bing Webmaster Tools`);
                resolve(false);
              } else {
                log(`⚠️ ${engine.name} returned status ${res.statusCode} (attempt ${attempts})`);
                if (data && data.length < 200) log(`Response: ${data.substring(0, 100)}`);
                resolve(false);
              }
            });
          });

          req.on('error', (err) => {
            log(`❌ Failed to notify ${engine.name} (attempt ${attempts}): ${err.message}`);
            resolve(false);
          });

          req.setTimeout(10000, () => {
            req.destroy();
            log(`⏰ Timeout notifying ${engine.name} (attempt ${attempts})`);
            resolve(false);
          });
        });

        if (success) break; // Success, no need to retry

        if (attempts < maxAttempts && !engine.deprecated) {
          log(`🔄 Retrying ${engine.name} in 3 seconds...`);
          await new Promise(resolve => setTimeout(resolve, 3000));
        } else {
          break; // Don't retry deprecated services
        }

      } catch (error) {
        log(`❌ Error notifying ${engine.name} (attempt ${attempts}): ${error.message}`);
        if (attempts < maxAttempts) {
          await new Promise(resolve => setTimeout(resolve, 3000));
        }
      }
    }
  }

  // Provide modern SEO guidance
  log('');
  log('📋 MODERN SEO SUBMISSION METHODS:');
  log('🔗 Google Search Console: https://search.google.com/search-console');
  log('🔗 Bing Webmaster Tools: https://www.bing.com/webmasters');
  log(`📄 Your sitemap URL: ${siteUrl}/sitemap.xml`);
  log('💡 Submit your sitemap manually for best results');
}

/**
 * Main function to run the automatic update
 */
async function runAutoUpdate() {
  let dbHandler = null;

  try {
    log('🔄 Starting automatic SEO update...');

    // Generate fresh sitemap
    log('🗺️ Generating fresh sitemap...');
    await generateSitemap();

    // Notify search engines
    await notifySearchEngines();

    log('✅ Automatic SEO update completed successfully!');

  } catch (error) {
    log(`❌ Error during automatic SEO update: ${error.message}`);
    process.exit(1);
  } finally {
    // Ensure all database connections are properly closed
    try {
      const dbModule = await import('./database-connection-handler.js');
      dbHandler = dbModule.default;
      await dbHandler.close();
    } catch (closeError) {
      // If graceful close fails, force close
      if (dbHandler) {
        dbHandler.forceClose();
      }
    }
  }
}

// Ensure clean shutdown
process.on('SIGINT', async () => {
  console.log('\n🛑 Received SIGINT, cleaning up...');
  try {
    const dbModule = await import('./database-connection-handler.js');
    const dbHandler = dbModule.default;
    await dbHandler.close();
  } catch (error) {
    // Ignore cleanup errors
  }
  process.exit(0);
});

process.on('SIGTERM', async () => {
  console.log('\n🛑 Received SIGTERM, cleaning up...');
  try {
    const dbModule = await import('./database-connection-handler.js');
    const dbHandler = dbModule.default;
    await dbHandler.close();
  } catch (error) {
    // Ignore cleanup errors
  }
  process.exit(0);
});

// Run the update
runAutoUpdate();
