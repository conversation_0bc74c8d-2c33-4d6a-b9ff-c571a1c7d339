# 🚀 StreamDB Enhanced Maintenance Quick Reference

## 📋 DAILY OPERATIONS

### Check Maintenance Status
```bash
# Check if maintenance scripts are running
ps aux | grep -E "enhanced.*maintenance"

# Check lock files
ls -la /var/run/streamdb-enhanced-*-maintenance.lock

# View recent maintenance logs
tail -50 /var/log/streamdb-maintenance/enhanced-*-maintenance-*.log
```

### Monitor System Health
```bash
# Backend Server Health Check
systemctl status mysql fastpanel
pm2 list
df -h
free -h

# Reverse Proxy Health Check
systemctl status nginx ufw
nginx -t
curl -I https://streamdb.online
```

### View Maintenance Logs
```bash
# Latest backend maintenance log
ls -t /var/log/streamdb-maintenance/enhanced-backend-maintenance-*.log | head -1 | xargs tail -50

# Latest proxy maintenance log
ls -t /var/log/streamdb-maintenance/enhanced-proxy-maintenance-*.log | head -1 | xargs tail -50

# Search for errors in logs
grep -i error /var/log/streamdb-maintenance/enhanced-*-maintenance-*.log | tail -20
```

---

## 🔧 MANUAL MAINTENANCE COMMANDS

### Run Maintenance Manually
```bash
# Backend server maintenance (use with caution)
/usr/local/bin/enhanced-backend-maintenance.sh

# Reverse proxy maintenance (use with caution)
/usr/local/bin/enhanced-reverse-proxy-maintenance.sh

# Validation tests only
/usr/local/bin/enhanced-validation-scripts.sh
```

### Emergency Service Management
```bash
# Restart critical services (Backend)
systemctl restart mysql
systemctl restart fastpanel
pm2 restart streamdb-online

# Restart critical services (Proxy)
nginx -t && systemctl restart nginx
systemctl restart ufw
```

### Backup Management
```bash
# List recent backups
ls -la /var/backups/streamdb-maintenance/*/

# Create manual database backup
mysqldump -u stream_db_admin --single-transaction stream_db | gzip > manual-backup-$(date +%Y%m%d).sql.gz

# Create manual configuration backup
tar -czf manual-config-backup-$(date +%Y%m%d).tar.gz /etc/nginx/ /etc/mysql/ /etc/fastpanel/
```

---

## 📊 MONITORING COMMANDS

### Resource Monitoring
```bash
# Disk usage
df -h

# Memory usage
free -h

# CPU usage and load
htop
uptime

# Network connections
ss -tuln | grep -E ":80|:443|:3001|:8888"
```

### Service Status Checks
```bash
# All critical services status
systemctl status mysql fastpanel nginx ufw ssh

# PM2 application status
pm2 list
pm2 monit

# Database connectivity
mysql -u stream_db_admin -e "SELECT 1;" stream_db
```

### SSL Certificate Monitoring
```bash
# Check certificate expiration
openssl x509 -in /etc/ssl/certs/cloudflare-origin.pem -noout -enddate

# Verify certificate chain
openssl s_client -connect streamdb.online:443 -servername streamdb.online

# Check certificate and key match
openssl x509 -in /etc/ssl/certs/cloudflare-origin.pem -noout -modulus | openssl md5
openssl rsa -in /etc/ssl/private/cloudflare-origin.key -noout -modulus | openssl md5
```

---

## ⚙️ CONFIGURATION MANAGEMENT

### View Current Configuration
```bash
# Backend configuration
cat /etc/streamdb-maintenance/backend-config.conf

# Proxy configuration
cat /etc/streamdb-maintenance/proxy-config.conf

# Cron configuration
cat /etc/cron.d/streamdb-enhanced-maintenance
```

### Modify Maintenance Schedule
```bash
# Edit cron schedule
nano /etc/cron.d/streamdb-enhanced-maintenance

# Restart cron service
systemctl restart cron

# Verify cron job
crontab -l
```

### Update Configuration Thresholds
```bash
# Backend thresholds
nano /etc/streamdb-maintenance/backend-config.conf

# Proxy thresholds
nano /etc/streamdb-maintenance/proxy-config.conf
```

---

## 🚨 TROUBLESHOOTING QUICK FIXES

### Script Won't Run
```bash
# Check permissions
ls -la /usr/local/bin/enhanced-*-maintenance.sh

# Fix permissions
chmod +x /usr/local/bin/enhanced-*-maintenance.sh
chown root:root /usr/local/bin/enhanced-*-maintenance.sh

# Test script syntax
bash -n /usr/local/bin/enhanced-backend-maintenance.sh
bash -n /usr/local/bin/enhanced-reverse-proxy-maintenance.sh
```

### Database Issues
```bash
# Test database connection
mysql -u stream_db_admin -e "SELECT 1;" stream_db

# Check MySQL service
systemctl status mysql

# Restart MySQL if needed
systemctl restart mysql
```

### Nginx Issues
```bash
# Test nginx configuration
nginx -t

# Check nginx status
systemctl status nginx

# Reload nginx configuration
nginx -s reload

# Restart nginx if needed
systemctl restart nginx
```

### High Resource Usage
```bash
# Check running processes
htop

# Check disk usage
df -h

# Check memory usage
free -h

# Kill maintenance if stuck
pkill -f "enhanced.*maintenance"
```

---

## 📁 FILE LOCATIONS REFERENCE

### Script Locations
```
/usr/local/bin/enhanced-backend-maintenance.sh
/usr/local/bin/enhanced-reverse-proxy-maintenance.sh
/usr/local/bin/enhanced-validation-scripts.sh
/usr/local/bin/enhanced-cron-configuration.sh
```

### Configuration Files
```
/etc/streamdb-maintenance/backend-config.conf
/etc/streamdb-maintenance/proxy-config.conf
/etc/cron.d/streamdb-enhanced-maintenance
```

### Log Files
```
/var/log/streamdb-maintenance/enhanced-backend-maintenance-*.log
/var/log/streamdb-maintenance/enhanced-proxy-maintenance-*.log
/var/log/streamdb-maintenance/cron-enhanced.log
```

### Backup Locations
```
/var/backups/streamdb-maintenance/configs/
/var/backups/streamdb-maintenance/database/
/var/backups/streamdb-maintenance/nginx-configs/
```

### Lock Files
```
/var/run/streamdb-enhanced-backend-maintenance.lock
/var/run/streamdb-enhanced-proxy-maintenance.lock
```

---

## 🔄 MAINTENANCE SCHEDULE

### Weekly Schedule
- **Thursday 00:00** - Backend Server Enhanced Maintenance
- **Thursday 00:30** - Reverse Proxy Enhanced Maintenance
- **Thursday 01:00** - Maintenance Complete (estimated)

### What Gets Maintained
**Backend Server:**
- ✅ Security updates and patches
- ✅ Database optimization and backup
- ✅ System cleanup and log rotation
- ✅ Service health monitoring
- ✅ Performance optimization
- ✅ Resource usage monitoring

**Reverse Proxy:**
- ✅ Security updates and patches
- ✅ SSL certificate monitoring
- ✅ Nginx optimization
- ✅ Firewall verification
- ✅ Backend connectivity checks
- ✅ Performance monitoring

---

## 📞 EMERGENCY CONTACTS

### Critical Issues
- **Email:** <EMAIL>
- **Server Issues:** Check logs first, then restart services
- **Database Issues:** Restore from latest backup if needed
- **SSL Issues:** Verify certificate expiration and renewal

### Escalation Procedure
1. Check maintenance logs for errors
2. Attempt service restart
3. Restore from latest backup if needed
4. Contact system administrator
5. Document issue for future prevention

---

**🎯 Remember:** Always check logs first, test changes in a safe environment, and maintain regular backups!
