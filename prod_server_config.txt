=== StreamDB Online Production Server Configuration Report ===
Generated on: Mon Jul 21 17:21:38 UTC 2025
Server IP: ***********

=== SYSTEM INFORMATION ===
OS: Ubuntu 24.04.2 LTS
Kernel: 6.8.0-63-generic
Architecture: x86_64
Uptime:  17:21:38 up 17 days, 14:03,  5 users,  load average: 0.13, 0.06, 0.01
CPU: QEMU Virtual CPU version 2.5+ pc-i440fx-9.0 CPU @ 2.0GHz
Memory: 
Disk Usage: 38G total, 6.0G used, 32G available

=== NETWORK CONFIGURATION ===
Network Interfaces:
1: lo: <LOOPBACK,UP,LOWER_UP> mtu 65536 qdisc noqueue state UNKNOWN group default qlen 1000
    inet 127.0.0.1/8 scope host lo
2: eth0: <BROADCAST,MULTICAST,UP,LOWER_UP> mtu 1500 qdisc fq_codel state UP group default qlen 1000
    inet ***********/24 brd *********** scope global eth0

Active Network Connections:
tcp        0      0 ***********:443         0.0.0.0:*               LISTEN      506146/nginx: maste 
tcp        0      0 127.0.0.1:3306          0.0.0.0:*               LISTEN      506512/mysqld       
tcp        0      0 ***********:80          0.0.0.0:*               LISTEN      506146/nginx: maste 
tcp        0      0 0.0.0.0:8888            0.0.0.0:*               LISTEN      506145/nginx: maste 
tcp6       0      0 :::3001                 :::*                    LISTEN      657889/node /var/ww 

=== FASTPANEL CONFIGURATION ===
FastPanel Status:

FastPanel Configuration Files:
/etc/logrotate.d/fastpanel2-backup
/etc/logrotate.d/fastpanel2
/etc/logrotate.d/fastpanel2-nginx
/etc/apt/trusted.gpg.d/RPM-GPG-KEY-fastpanel.asc
/etc/apt/sources.list.d/fastpanel2.list
/etc/nginx/conf.d/99-fastpanel.conf
/etc/tmpfiles.d/fastpanel.conf
/etc/ssh/ca/fastpanel
/etc/ssh/ca/fastpanel.pub
/etc/init.d/fastpanel2-apps
/etc/init.d/fastpanel2
/etc/apache2/conf.d/99-fastpanel.conf
/etc/mysql/my.cnf.fastpanel/99-fastpanel.cnf
/etc/cron.d/fastpanel2-session-cleaner
/etc/cron.d/fastpanel2-fcgi-cleaner
/etc/cron.d/fastpanel2
/etc/cron.d/fastpanel2-updater

=== WEB SERVER CONFIGURATION ===
Apache Status:
● apache2.service - The Apache HTTP Server
     Loaded: loaded (/usr/lib/systemd/system/apache2.service; enabled; preset: enabled)
     Active: active (running) since Fri 2025-07-18 06:39:21 UTC; 3 days ago
       Docs: https://httpd.apache.org/docs/2.4/
    Process: 577547 ExecStart=/usr/sbin/apachectl start (code=exited, status=0/SUCCESS)
    Process: 647580 ExecReload=/usr/sbin/apachectl graceful (code=exited, status=0/SUCCESS)
   Main PID: 577551 (apache2)
      Tasks: 8 (limit: 4655)
     Memory: 21.2M (peak: 33.2M)
        CPU: 1min 727ms

Nginx Status:
● nginx.service - nginx - high performance web server
     Loaded: loaded (/usr/lib/systemd/system/nginx.service; enabled; preset: enabled)
     Active: active (running) since Wed 2025-07-16 06:34:51 UTC; 5 days ago
       Docs: https://nginx.org/en/docs/
   Main PID: 506146 (nginx)
      Tasks: 3 (limit: 4655)
     Memory: 5.0M (peak: 7.4M)
        CPU: 30.146s
     CGroup: /system.slice/nginx.service
             ├─506146 "nginx: master process /usr/sbin/nginx -c /etc/nginx/nginx.conf"

Web Server Configuration Files:
/etc/apache2/fastpanel2-sites/streamdb_root
/etc/apache2/fastpanel2-sites/streamdb_root/streamdb.online.conf
/etc/apache2/sites-available/000-default.conf
/etc/apache2/sites-available/default-ssl.conf
/etc/apache2/fastpanel2-available/streamdb_root
/etc/apache2/fastpanel2-available/streamdb_root/streamdb.online.conf
/etc/nginx/fastpanel2-sites/streamdb_root
/etc/nginx/fastpanel2-sites/streamdb_root/streamdb.online.conf
/etc/nginx/fastpanel2-sites/streamdb_root/streamdb.online.includes
/etc/nginx/conf.d/default.conf
/etc/nginx/sites-enabled/streamdb-backend
/etc/nginx/sites-available/streamdb-backend
/etc/nginx/fastpanel2-available/streamdb_root
/etc/nginx/fastpanel2-available/streamdb_root/streamdb.online.conf

=== MYSQL DATABASE CONFIGURATION ===
MySQL Status:
● mysql.service - MySQL Community Server
     Loaded: loaded (/usr/lib/systemd/system/mysql.service; enabled; preset: enabled)
     Active: active (running) since Wed 2025-07-16 06:34:55 UTC; 5 days ago
   Main PID: 506512 (mysqld)
     Status: "Server is operational"
      Tasks: 40 (limit: 4655)
     Memory: 297.4M (peak: 298.7M)
        CPU: 1h 58min 30.988s
     CGroup: /system.slice/mysql.service
             └─506512 /usr/sbin/mysqld

MySQL Configuration:
Variable_name	Value
socket	/var/run/mysqld/mysqld.sock
Variable_name	Value
port	3306
Variable_name	Value
bind_address	127.0.0.1
Variable_name	Value
datadir	/var/lib/mysql/

Database Information:
Database
information_schema
performance_schema
stream_db
Tables_in_stream_db
ad_blocker_tracking
admin_security_logs
admin_users
auth_tokens
categories
content
content_section_mappings
content_sections
episodes
login_attempts
password_reset_tokens
seasons
section_categories
section_content_types
security_logs
sessions
user_sessions
total_content
9
total_seasons
5
total_episodes
9

MySQL Socket Information:
srwxrwxrwx 1 mysql mysql 0 Jul 16 06:34 /var/run/mysqld/mysqld.sock

=== NODE.JS APPLICATION CONFIGURATION ===
Node.js Version:
v20.19.3

NPM Version:
10.8.2

PM2 Status:
┌────┬────────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id │ name               │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├────┼────────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 0  │ streamdb-online    │ default     │ 1.0.0   │ fork    │ 657889   │ 6h     │ 6752 │ online    │ 0%       │ 84.0mb   │ root     │ disabled │
└────┴────────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘

PM2 Process Details:

=== APPLICATION DIRECTORY STRUCTURE ===
StreamDB Application Directory:

Server Directory Contents:

Environment File Status:

=== SSL/TLS CONFIGURATION ===
SSL Certificates:

=== FIREWALL CONFIGURATION ===
UFW Status:
Status: active

To                         Action      From
--                         ------      ----
22/tcp                     ALLOW       Anywhere                  
80/tcp                     ALLOW       Anywhere                  
443/tcp                    ALLOW       Anywhere                  
8888/tcp                   ALLOW       Anywhere                  
Anywhere                   ALLOW       *************             
22/tcp (v6)                ALLOW       Anywhere (v6)             
80/tcp (v6)                ALLOW       Anywhere (v6)             
443/tcp (v6)               ALLOW       Anywhere (v6)             
8888/tcp (v6)              ALLOW       Anywhere (v6)             


IPTables Rules:
Chain INPUT (policy DROP)
target     prot opt source               destination         
ufw-before-logging-input  0    --  0.0.0.0/0            0.0.0.0/0           
ufw-before-input  0    --  0.0.0.0/0            0.0.0.0/0           
ufw-after-input  0    --  0.0.0.0/0            0.0.0.0/0           
ufw-after-logging-input  0    --  0.0.0.0/0            0.0.0.0/0           
ufw-reject-input  0    --  0.0.0.0/0            0.0.0.0/0           
ufw-track-input  0    --  0.0.0.0/0            0.0.0.0/0           

Chain FORWARD (policy DROP)
target     prot opt source               destination         
ufw-before-logging-forward  0    --  0.0.0.0/0            0.0.0.0/0           
ufw-before-forward  0    --  0.0.0.0/0            0.0.0.0/0           
ufw-after-forward  0    --  0.0.0.0/0            0.0.0.0/0           
ufw-after-logging-forward  0    --  0.0.0.0/0            0.0.0.0/0           
ufw-reject-forward  0    --  0.0.0.0/0            0.0.0.0/0           
ufw-track-forward  0    --  0.0.0.0/0            0.0.0.0/0           

Chain OUTPUT (policy ACCEPT)
target     prot opt source               destination         

=== SYSTEM SERVICES ===
All Running Services:
  apache2.service             loaded active running The Apache HTTP Server
  fastpanel2-apps.service     loaded active running FASTPANEL applications pool
  fastpanel2-nginx.service    loaded active running FASTPANEL web server
  fastpanel2.service          loaded active running FASTPANEL
  mysql.service               loaded active running MySQL Community Server
  nginx.service               loaded active running nginx - high performance web server

=== LOG FILES LOCATIONS ===
Application Log Files:

System Log Files:
drwxr-x---   2 <USER>        <GROUP>                 4096 Jul 21 00:00 apache2
drwxr-x--x   2 <USER>        <GROUP>                4096 Jul 21 00:00 fastpanel2
drwxr-x---   2 <USER>       <GROUP>                 4096 Jul 21 00:00 mysql
drwxr-xr-x   2 <USER>        <GROUP>                4096 Jul 21 00:00 nginx

=== CRON JOBS ===
Root Cron Jobs:

StreamDB User Cron Jobs:

=== BACKUP CONFIGURATION ===
Backup Scripts:

=== RESOURCE USAGE ===
Current Process Usage:
root       40679  0.0  1.3 996428 53628 ?        Ssl  Jul04   0:39 node /usr/bin/pm2 logs streamdb-main --lines 20
root      144340  0.0  1.3 996064 53468 ?        Ssl  Jul07   0:32 node /usr/bin/pm2 logs index --lines 20
root      506145  0.0  0.0  12396  3072 ?        Ss   Jul16   0:00 nginx: master process /usr/local/sbin/fastpanel2-nginx
root      506146  0.0  0.1  14424  6980 ?        Ss   Jul16   0:00 nginx: master process /usr/sbin/nginx -c /etc/nginx/nginx.conf
www-data  506147  0.0  0.1  14392  7040 ?        S    Jul16   2:26 nginx: worker process
mysql     506512  1.5  8.3 1595988 333548 ?      Ssl  Jul16 118:31 /usr/sbin/mysqld
root      553961  0.2  1.3 2001636 55120 ?       Ssl  Jul18  14:30 /usr/local/fastpanel2/fastpanel start
root      553970  0.0  0.9 1852052 37344 ?       Ssl  Jul18   0:59 /usr/local/fastpanel2/fastpanel panel stats
www-data  554068  0.0  0.2  15780  9232 ?        S    Jul18   0:09 nginx: worker process
www-data  554069  0.0  0.2  15780  9488 ?        S    Jul18   0:10 nginx: worker process
root      577551  0.0  0.8 232396 32908 ?        Ss   Jul18   0:32 /usr/sbin/apache2 -k start
www-data  647589  0.0  0.2  67244 11132 ?        S    00:00   0:00 /usr/sbin/apache2 -k start
www-data  647590  0.0  0.2  68420 10620 ?        S    00:00   0:02 /usr/sbin/apache2 -k start
www-data  647592  0.0  0.3 233076 15040 ?        S    00:00   0:00 /usr/sbin/apache2 -k start
www-data  647593  0.0  0.3 233076 15296 ?        S    00:00   0:00 /usr/sbin/apache2 -k start
www-data  647594  0.0  0.3 233076 15040 ?        S    00:00   0:00 /usr/sbin/apache2 -k start
www-data  647595  0.0  0.3 233076 15296 ?        S    00:00   0:00 /usr/sbin/apache2 -k start
www-data  649313  0.0  0.3 233076 15296 ?        S    01:56   0:00 /usr/sbin/apache2 -k start
root      657889  0.5  2.1 11795844 85676 ?      Ssl  11:19   1:59 node /var/www/streamdb_root/data/www/streamdb.online/serve

Memory Usage by Process:
USER         PID %CPU %MEM    VSZ   RSS TTY      STAT START   TIME COMMAND
mysql     506512  1.5  8.3 1595988 333548 ?      Ssl  Jul16 118:31 /usr/sbin/mysqld
root      506125  0.0  2.1 153108 86872 ?        S<s  Jul16   2:06 /usr/lib/systemd/systemd-journald
root      506497  0.1  2.1 724308 86776 ?        Ssl  Jul16  14:13 /usr/bin/python3 /usr/bin/fail2ban-server -xf start
root      657889  0.5  2.1 11795844 85676 ?      Ssl  11:19   1:59 node /var/www/streamdb_root/data/www/streamdb.online/serve
root       10601  0.0  1.7 1300908 70212 ?       Ssl  Jul04  23:13 PM2 v6.0.8: God Daemon (/root/.pm2)
root      553961  0.2  1.3 2001636 55120 ?       Ssl  Jul18  14:30 /usr/local/fastpanel2/fastpanel start
root       40679  0.0  1.3 996428 53628 ?        Ssl  Jul04   0:39 node /usr/bin/pm2 logs streamdb-main --lines 20
root      144340  0.0  1.3 996064 53468 ?        Ssl  Jul07   0:32 node /usr/bin/pm2 logs index --lines 20
Debian-+  507606  0.0  1.0  68608 40880 ?        Ss   Jul16   0:02 /usr/sbin/exim4 -bdf -q30m

=== CONFIGURATION FILES CONTENT ===
Apache Virtual Host Configuration:
=== /etc/apache2/sites-enabled ===

=== REPORT COMPLETE ===
Report saved to: /tmp/prod_server_config.txt
File size: 16K
