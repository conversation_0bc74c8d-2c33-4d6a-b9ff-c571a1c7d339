#!/bin/bash

# Backend Server Documentation Script
# Run this on your Backend Server (***********)
# This script will generate comprehensive documentation of your backend server setup

echo "=== BACKEND SERVER DOCUMENTATION GENERATOR ==="
echo "Starting documentation generation at $(date)"
echo "Server IP: $(curl -s ifconfig.me)"

# Create output directory
OUTPUT_DIR="/tmp/backend-server-docs"
mkdir -p "$OUTPUT_DIR"

# Main documentation file
DOC_FILE="$OUTPUT_DIR/backend-server-configuration.md"

cat > "$DOC_FILE" << 'EOF'
# Backend Server Configuration Documentation

## Server Information
EOF

echo "## Server Details" >> "$DOC_FILE"
echo "- **Server IP:** $(curl -s ifconfig.me)" >> "$DOC_FILE"
echo "- **Hostname:** $(hostname)" >> "$DOC_FILE"
echo "- **OS:** $(cat /etc/os-release | grep PRETTY_NAME | cut -d'"' -f2)" >> "$DOC_FILE"
echo "- **Kernel:** $(uname -r)" >> "$DOC_FILE"
echo "- **Architecture:** $(uname -m)" >> "$DOC_FILE"
echo "- **Documentation Generated:** $(date)" >> "$DOC_FILE"
echo "" >> "$DOC_FILE"

# System Resources
echo "## System Resources" >> "$DOC_FILE"
echo "### CPU Information" >> "$DOC_FILE"
echo '```' >> "$DOC_FILE"
lscpu >> "$DOC_FILE"
echo '```' >> "$DOC_FILE"
echo "" >> "$DOC_FILE"

echo "### Memory Information" >> "$DOC_FILE"
echo '```' >> "$DOC_FILE"
free -h >> "$DOC_FILE"
echo '```' >> "$DOC_FILE"
echo "" >> "$DOC_FILE"

echo "### Disk Usage" >> "$DOC_FILE"
echo '```' >> "$DOC_FILE"
df -h >> "$DOC_FILE"
echo '```' >> "$DOC_FILE"
echo "" >> "$DOC_FILE"

# Network Configuration
echo "## Network Configuration" >> "$DOC_FILE"
echo "### Network Interfaces" >> "$DOC_FILE"
echo '```' >> "$DOC_FILE"
ip addr show >> "$DOC_FILE"
echo '```' >> "$DOC_FILE"
echo "" >> "$DOC_FILE"

echo "### Routing Table" >> "$DOC_FILE"
echo '```' >> "$DOC_FILE"
ip route show >> "$DOC_FILE"
echo '```' >> "$DOC_FILE"
echo "" >> "$DOC_FILE"

# FastPanel Configuration
echo "## FastPanel Control Panel" >> "$DOC_FILE"
echo "### FastPanel Service Status" >> "$DOC_FILE"
echo '```' >> "$DOC_FILE"
systemctl status fastpanel 2>/dev/null || echo "FastPanel service not found via systemctl" >> "$DOC_FILE"
echo '```' >> "$DOC_FILE"
echo "" >> "$DOC_FILE"

echo "### FastPanel Configuration Files" >> "$DOC_FILE"
for config_path in "/etc/fastpanel" "/opt/fastpanel" "/usr/local/fastpanel"; do
    if [ -d "$config_path" ]; then
        echo "#### FastPanel Directory: $config_path" >> "$DOC_FILE"
        echo '```' >> "$DOC_FILE"
        find "$config_path" -type f -name "*.conf" -o -name "*.cfg" -o -name "*.ini" 2>/dev/null | head -20 >> "$DOC_FILE"
        echo '```' >> "$DOC_FILE"
        echo "" >> "$DOC_FILE"
    fi
done

# Web Server Configuration
echo "## Web Server Configuration" >> "$DOC_FILE"

# Check for Apache
if command -v apache2 &> /dev/null || command -v httpd &> /dev/null; then
    echo "### Apache Configuration" >> "$DOC_FILE"
    echo "#### Apache Version" >> "$DOC_FILE"
    echo '```' >> "$DOC_FILE"
    apache2 -v 2>/dev/null || httpd -v 2>/dev/null >> "$DOC_FILE"
    echo '```' >> "$DOC_FILE"
    echo "" >> "$DOC_FILE"
    
    echo "#### Apache Service Status" >> "$DOC_FILE"
    echo '```' >> "$DOC_FILE"
    systemctl status apache2 2>/dev/null || systemctl status httpd 2>/dev/null >> "$DOC_FILE"
    echo '```' >> "$DOC_FILE"
    echo "" >> "$DOC_FILE"
    
    echo "#### Apache Main Configuration" >> "$DOC_FILE"
    for conf in "/etc/apache2/apache2.conf" "/etc/httpd/conf/httpd.conf"; do
        if [ -f "$conf" ]; then
            echo "##### Configuration: $conf" >> "$DOC_FILE"
            echo '```apache' >> "$DOC_FILE"
            cat "$conf" >> "$DOC_FILE"
            echo '```' >> "$DOC_FILE"
            echo "" >> "$DOC_FILE"
        fi
    done
    
    echo "#### Apache Virtual Hosts" >> "$DOC_FILE"
    for vhost_dir in "/etc/apache2/sites-enabled" "/etc/httpd/conf.d"; do
        if [ -d "$vhost_dir" ]; then
            for vhost in "$vhost_dir"/*; do
                if [ -f "$vhost" ]; then
                    echo "##### Virtual Host: $(basename $vhost)" >> "$DOC_FILE"
                    echo '```apache' >> "$DOC_FILE"
                    cat "$vhost" >> "$DOC_FILE"
                    echo '```' >> "$DOC_FILE"
                    echo "" >> "$DOC_FILE"
                fi
            done
        fi
    done
fi

# Check for NGINX
if command -v nginx &> /dev/null; then
    echo "### NGINX Configuration" >> "$DOC_FILE"
    echo "#### NGINX Version" >> "$DOC_FILE"
    echo '```' >> "$DOC_FILE"
    nginx -v 2>&1 >> "$DOC_FILE"
    echo '```' >> "$DOC_FILE"
    echo "" >> "$DOC_FILE"
    
    echo "#### NGINX Service Status" >> "$DOC_FILE"
    echo '```' >> "$DOC_FILE"
    systemctl status nginx >> "$DOC_FILE"
    echo '```' >> "$DOC_FILE"
    echo "" >> "$DOC_FILE"
fi

# MySQL Database Configuration
echo "## MySQL Database Configuration" >> "$DOC_FILE"
echo "### MySQL Service Status" >> "$DOC_FILE"
echo '```' >> "$DOC_FILE"
systemctl status mysql 2>/dev/null || systemctl status mariadb 2>/dev/null || echo "MySQL/MariaDB service not found" >> "$DOC_FILE"
echo '```' >> "$DOC_FILE"
echo "" >> "$DOC_FILE"

echo "### MySQL Version" >> "$DOC_FILE"
echo '```' >> "$DOC_FILE"
mysql --version 2>/dev/null || echo "MySQL client not found" >> "$DOC_FILE"
echo '```' >> "$DOC_FILE"
echo "" >> "$DOC_FILE"

echo "### MySQL Configuration" >> "$DOC_FILE"
for conf in "/etc/mysql/my.cnf" "/etc/my.cnf" "/usr/local/mysql/my.cnf"; do
    if [ -f "$conf" ]; then
        echo "#### MySQL Config: $conf" >> "$DOC_FILE"
        echo '```ini' >> "$DOC_FILE"
        cat "$conf" >> "$DOC_FILE"
        echo '```' >> "$DOC_FILE"
        echo "" >> "$DOC_FILE"
    fi
done

# Check for additional MySQL config directories
for conf_dir in "/etc/mysql/conf.d" "/etc/mysql/mysql.conf.d"; do
    if [ -d "$conf_dir" ]; then
        echo "#### MySQL Config Directory: $conf_dir" >> "$DOC_FILE"
        for conf in "$conf_dir"/*.cnf; do
            if [ -f "$conf" ]; then
                echo "##### Config: $(basename $conf)" >> "$DOC_FILE"
                echo '```ini' >> "$DOC_FILE"
                cat "$conf" >> "$DOC_FILE"
                echo '```' >> "$DOC_FILE"
                echo "" >> "$DOC_FILE"
            fi
        done
    fi
done

# PHP Configuration
echo "## PHP Configuration" >> "$DOC_FILE"
echo "### PHP Version" >> "$DOC_FILE"
echo '```' >> "$DOC_FILE"
php --version 2>/dev/null || echo "PHP not found" >> "$DOC_FILE"
echo '```' >> "$DOC_FILE"
echo "" >> "$DOC_FILE"

echo "### PHP Configuration" >> "$DOC_FILE"
echo '```' >> "$DOC_FILE"
php --ini 2>/dev/null || echo "PHP configuration not accessible" >> "$DOC_FILE"
echo '```' >> "$DOC_FILE"
echo "" >> "$DOC_FILE"

# Website Files
echo "## Website Configuration" >> "$DOC_FILE"
echo "### Document Root Locations" >> "$DOC_FILE"
echo '```' >> "$DOC_FILE"
find /var/www /home /opt -name "streamdb.online" -type d 2>/dev/null >> "$DOC_FILE"
find /var/www /home /opt -name "public_html" -type d 2>/dev/null >> "$DOC_FILE"
echo '```' >> "$DOC_FILE"
echo "" >> "$DOC_FILE"

# SSL Certificates
echo "## SSL Certificates" >> "$DOC_FILE"
echo "### SSL Certificate Locations" >> "$DOC_FILE"
echo '```' >> "$DOC_FILE"
find /etc/ssl /etc/letsencrypt /etc/apache2 /etc/httpd -name "*.crt" -o -name "*.pem" -o -name "*.key" 2>/dev/null >> "$DOC_FILE"
echo '```' >> "$DOC_FILE"
echo "" >> "$DOC_FILE"

# Running Services
echo "## Running Services" >> "$DOC_FILE"
echo "### Active Services" >> "$DOC_FILE"
echo '```' >> "$DOC_FILE"
systemctl list-units --type=service --state=active >> "$DOC_FILE"
echo '```' >> "$DOC_FILE"
echo "" >> "$DOC_FILE"

# Port Usage
echo "### Open Ports" >> "$DOC_FILE"
echo '```' >> "$DOC_FILE"
netstat -tulpn >> "$DOC_FILE"
echo '```' >> "$DOC_FILE"
echo "" >> "$DOC_FILE"

# Firewall Configuration
echo "## Firewall Configuration" >> "$DOC_FILE"
if command -v ufw &> /dev/null; then
    echo "### UFW Status" >> "$DOC_FILE"
    echo '```' >> "$DOC_FILE"
    ufw status verbose >> "$DOC_FILE"
    echo '```' >> "$DOC_FILE"
    echo "" >> "$DOC_FILE"
fi

echo "Documentation generated successfully!"
echo "Output file: $DOC_FILE"
echo ""
echo "To download this file, you can use:"
echo "scp root@***********:$DOC_FILE ./backend-server-config.md"
echo ""
echo "Or view it directly:"
echo "cat $DOC_FILE"
